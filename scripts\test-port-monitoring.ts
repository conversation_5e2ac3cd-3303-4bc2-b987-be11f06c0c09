/**
 * 端口监控功能测试脚本
 * 测试端口状态检测和监控功能
 */

import 'reflect-metadata';
import { Container } from 'inversify';
import { EventBus } from '../src/main/core/event-bus';
import { SimpleLogger } from '../src/main/services/logger-simple.service';
import { PortMonitor } from '../src/main/services/port-monitor.service';
import { TYPES, CoreEvents } from '../src/shared/types';
import * as net from 'net';

async function testPortMonitoring() {
  console.log('🔌 开始测试端口监控功能...\n');

  try {
    // 创建依赖注入容器
    const container = new Container();
    
    container.bind(TYPES.EventBus).to(EventBus).inSingletonScope();
    container.bind(TYPES.Logger).to(SimpleLogger).inSingletonScope();
    container.bind(TYPES.PortMonitor).to(PortMonitor).inSingletonScope();

    const eventBus = container.get<EventBus>(TYPES.EventBus);
    const portMonitor = container.get<PortMonitor>(TYPES.PortMonitor);

    console.log('✅ 依赖注入容器创建成功');

    // 监听端口事件
    let portEvents: string[] = [];
    
    eventBus.on(CoreEvents.PORT_AVAILABLE, (event) => {
      portEvents.push(`${event.payload.port}:AVAILABLE`);
      console.log(`✅ 端口可用: ${event.payload.port}`);
    });

    eventBus.on(CoreEvents.PORT_UNAVAILABLE, (event) => {
      portEvents.push(`${event.payload.port}:UNAVAILABLE`);
      console.log(`❌ 端口不可用: ${event.payload.port}`);
    });

    console.log('✅ 事件监听器设置完成');

    // 测试1: 检查常见端口
    console.log('\n🧪 测试1: 检查常见端口状态...');
    const commonPorts = [80, 443, 3000, 5173, 8080];
    
    for (const port of commonPorts) {
      const available = await portMonitor.checkPort(port);
      console.log(`端口 ${port}: ${available ? '可用' : '被占用'}`);
    }

    // 测试2: 批量检查端口
    console.log('\n🧪 测试2: 批量检查端口...');
    const testPorts = [3001, 3002, 3003, 3004, 3005];
    const results = await portMonitor.checkMultiplePorts(testPorts);
    
    console.log('批量检查结果:');
    results.forEach((available, port) => {
      console.log(`- 端口 ${port}: ${available ? '可用' : '被占用'}`);
    });

    // 测试3: 创建测试服务器并监控
    console.log('\n🧪 测试3: 创建测试服务器并监控...');
    const testPort = 3333;
    
    // 先检查端口是否可用
    const initiallyAvailable = await portMonitor.checkPort(testPort);
    console.log(`测试端口 ${testPort} 初始状态: ${initiallyAvailable ? '可用' : '被占用'}`);

    // 开始监控端口
    console.log(`开始监控端口 ${testPort}...`);
    const watcher = portMonitor.watch(testPort, (available) => {
      console.log(`🔍 端口 ${testPort} 状态变化: ${available ? '可用' : '被占用'}`);
    });

    // 等待一下让监控开始
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 创建测试服务器
    console.log(`在端口 ${testPort} 上启动测试服务器...`);
    const server = net.createServer();
    
    await new Promise<void>((resolve, reject) => {
      server.listen(testPort, () => {
        console.log(`✅ 测试服务器已启动在端口 ${testPort}`);
        resolve();
      });
      
      server.on('error', (error) => {
        console.log(`❌ 服务器启动失败: ${error.message}`);
        reject(error);
      });
    });

    // 等待监控检测到变化
    await new Promise(resolve => setTimeout(resolve, 3000));

    // 停止服务器
    console.log('停止测试服务器...');
    await new Promise<void>((resolve) => {
      server.close(() => {
        console.log('✅ 测试服务器已停止');
        resolve();
      });
    });

    // 等待监控检测到变化
    await new Promise(resolve => setTimeout(resolve, 3000));

    // 停止监控
    console.log('停止端口监控...');
    watcher.stop();

    // 测试4: 获取监控的端口列表
    console.log('\n🧪 测试4: 获取监控状态...');
    const watchedPorts = portMonitor.getWatchedPorts();
    console.log(`当前监控的端口: ${watchedPorts.join(', ')}`);

    // 停止所有监控
    console.log('停止所有端口监控...');
    portMonitor.stopAll();

    // 统计信息
    console.log('\n📊 测试统计:');
    console.log(`- 触发的端口事件: ${portEvents.join(', ')}`);
    console.log(`- 事件总数: ${portEvents.length}`);
    console.log(`- 检查的端口数量: ${commonPorts.length + testPorts.length + 1}`);

    console.log('\n🎉 端口监控功能测试完成！');

  } catch (error) {
    console.error('\n❌ 测试失败:', error);
    process.exit(1);
  }
}

// 运行测试
testPortMonitoring().catch(console.error);
