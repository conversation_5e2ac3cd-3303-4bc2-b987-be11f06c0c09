"use strict";
/**
 * 核心功能测试脚本
 * 测试主要服务和架构是否正常工作
 */
Object.defineProperty(exports, "__esModule", { value: true });
require("reflect-metadata");
const inversify_1 = require("inversify");
const event_bus_1 = require("../src/main/core/event-bus");
const logger_simple_service_1 = require("../src/main/services/logger-simple.service");
const state_simple_service_1 = require("../src/main/services/state-simple.service");
const git_adapter_1 = require("../src/main/plugins/git/git-adapter");
const types_1 = require("../src/shared/types");
async function testCore() {
    console.log('🚀 开始测试 DevWorkbench 核心功能...\n');
    try {
        // 1. 测试依赖注入容器
        console.log('📦 测试依赖注入容器...');
        const container = new inversify_1.Container();
        // 注册服务
        container.bind(types_1.TYPES.EventBus).to(event_bus_1.EventBus).inSingletonScope();
        container.bind(types_1.TYPES.Logger).to(logger_simple_service_1.SimpleLogger).inSingletonScope();
        container.bind(types_1.TYPES.StateRepository).to(state_simple_service_1.SimpleStateRepository).inSingletonScope();
        container.bind(types_1.TYPES.VersionControlAdapter).to(git_adapter_1.GitAdapter).inSingletonScope();
        console.log('✅ 依赖注入容器创建成功');
        // 2. 测试事件总线
        console.log('\n📡 测试事件总线...');
        const eventBus = container.get(types_1.TYPES.EventBus);
        let eventReceived = false;
        eventBus.on('test.event', (event) => {
            console.log('✅ 事件接收成功:', event.type);
            eventReceived = true;
        });
        eventBus.emitEvent('test.event', { message: 'Hello World' });
        if (eventReceived) {
            console.log('✅ 事件总线工作正常');
        }
        else {
            throw new Error('事件总线测试失败');
        }
        // 3. 测试日志服务
        console.log('\n📝 测试日志服务...');
        const logger = container.get(types_1.TYPES.Logger);
        logger.info('测试日志信息');
        logger.debug('测试调试信息');
        logger.warn('测试警告信息');
        console.log('✅ 日志服务工作正常');
        // 4. 测试状态存储
        console.log('\n💾 测试状态存储...');
        const stateRepository = container.get(types_1.TYPES.StateRepository);
        // 测试存储和读取
        await stateRepository.set('test.key', { value: 'test data' });
        const storedData = await stateRepository.get('test.key');
        if (storedData && storedData.value === 'test data') {
            console.log('✅ 状态存储工作正常');
        }
        else {
            throw new Error('状态存储测试失败');
        }
        // 5. 测试Git适配器
        console.log('\n🔧 测试Git适配器...');
        const gitAdapter = container.get(types_1.TYPES.VersionControlAdapter);
        // 测试当前目录是否是Git仓库
        const isRepo = await gitAdapter.isRepository('.');
        console.log(`当前目录是Git仓库: ${isRepo}`);
        if (isRepo) {
            try {
                const status = await gitAdapter.getStatus('.');
                console.log('Git状态:', {
                    branch: status.branch,
                    hasChanges: status.hasUncommittedChanges
                });
            }
            catch (error) {
                console.log('Git状态获取失败（可能是权限问题）');
            }
        }
        console.log('✅ Git适配器工作正常');
        // 6. 测试完成
        console.log('\n🔍 基础服务测试完成');
        console.log('\n🎉 所有核心功能测试通过！');
        console.log('\n📊 测试总结:');
        console.log('- ✅ 依赖注入容器');
        console.log('- ✅ 事件总线');
        console.log('- ✅ 日志服务');
        console.log('- ✅ 状态存储');
        console.log('- ✅ Git适配器');
        console.log('\n🚀 DevWorkbench 核心架构运行正常！');
    }
    catch (error) {
        console.error('\n❌ 测试失败:', error);
        process.exit(1);
    }
}
// 运行测试
testCore().catch(console.error);
