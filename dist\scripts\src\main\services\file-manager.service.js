"use strict";
/**
 * 文件管理服务
 * 提供文件浏览、选择和操作功能
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileManagerService = void 0;
const inversify_1 = require("inversify");
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const types_1 = require("../../shared/types");
const event_bus_1 = require("../core/event-bus");
let FileManagerService = class FileManagerService {
    constructor(logger, eventBus) {
        this.logger = logger;
        this.eventBus = eventBus;
        this.watchers = new Map();
        this.gitIgnorePatterns = new Map();
    }
    /**
     * 获取目录树结构
     */
    async getDirectoryTree(rootPath, maxDepth = 3) {
        try {
            const absolutePath = path.resolve(rootPath);
            const stats = await fs.promises.stat(absolutePath);
            if (!stats.isDirectory()) {
                throw new Error('路径不是目录');
            }
            // 加载.gitignore规则
            await this.loadGitIgnorePatterns(absolutePath);
            const tree = await this.buildDirectoryTree(absolutePath, 0, maxDepth);
            this.logger.info(`构建目录树完成: ${rootPath}`);
            return tree;
        }
        catch (error) {
            this.logger.error(`构建目录树失败: ${rootPath}`, error);
            throw error;
        }
    }
    /**
     * 获取文件内容
     */
    async getFileContent(filePath) {
        try {
            const absolutePath = path.resolve(filePath);
            const stats = await fs.promises.stat(absolutePath);
            if (!stats.isFile()) {
                throw new Error('路径不是文件');
            }
            // 检查文件大小，避免读取过大的文件
            if (stats.size > 1024 * 1024) { // 1MB限制
                throw new Error('文件过大，无法读取');
            }
            const content = await fs.promises.readFile(absolutePath, 'utf-8');
            this.logger.debug(`读取文件内容: ${filePath}`);
            return content;
        }
        catch (error) {
            this.logger.error(`读取文件失败: ${filePath}`, error);
            throw error;
        }
    }
    /**
     * 获取文件统计信息
     */
    async getFileStats(filePath) {
        try {
            const absolutePath = path.resolve(filePath);
            const stats = await fs.promises.stat(absolutePath);
            return {
                size: stats.size,
                created: stats.birthtime,
                modified: stats.mtime,
                accessed: stats.atime,
                isDirectory: stats.isDirectory(),
                isFile: stats.isFile(),
                permissions: stats.mode.toString(8),
            };
        }
        catch (error) {
            this.logger.error(`获取文件统计失败: ${filePath}`, error);
            throw error;
        }
    }
    /**
     * 搜索文件
     */
    async searchFiles(rootPath, pattern) {
        try {
            const results = [];
            const regex = new RegExp(pattern, 'i');
            await this.searchInDirectory(rootPath, regex, results, 0, 5);
            this.logger.info(`文件搜索完成: ${pattern}, 找到 ${results.length} 个结果`);
            return results;
        }
        catch (error) {
            this.logger.error(`文件搜索失败: ${pattern}`, error);
            throw error;
        }
    }
    /**
     * 监听目录变化
     */
    async watchDirectory(dirPath) {
        try {
            if (this.watchers.has(dirPath)) {
                return; // 已经在监听
            }
            const watcher = fs.watch(dirPath, { recursive: true }, (eventType, filename) => {
                if (filename) {
                    this.eventBus.emitEvent(types_1.CoreEvents.CONFIG_CHANGED, {
                        type: 'file_changed',
                        eventType,
                        filename,
                        dirPath,
                        timestamp: new Date(),
                    });
                }
            });
            this.watchers.set(dirPath, watcher);
            this.logger.info(`开始监听目录: ${dirPath}`);
        }
        catch (error) {
            this.logger.error(`监听目录失败: ${dirPath}`, error);
            throw error;
        }
    }
    /**
     * 停止监听目录
     */
    async stopWatching(dirPath) {
        const watcher = this.watchers.get(dirPath);
        if (watcher) {
            watcher.close();
            this.watchers.delete(dirPath);
            this.logger.info(`停止监听目录: ${dirPath}`);
        }
    }
    /**
     * 构建目录树
     */
    async buildDirectoryTree(dirPath, currentDepth, maxDepth) {
        const stats = await fs.promises.stat(dirPath);
        const name = path.basename(dirPath);
        const node = {
            name,
            path: dirPath,
            type: stats.isDirectory() ? 'directory' : 'file',
            size: stats.size,
            modified: stats.mtime,
            extension: stats.isFile() ? path.extname(name) : undefined,
        };
        // 检查是否被git忽略
        node.isGitIgnored = this.isGitIgnored(dirPath);
        if (stats.isDirectory() && currentDepth < maxDepth) {
            try {
                const entries = await fs.promises.readdir(dirPath);
                const children = [];
                for (const entry of entries) {
                    // 跳过隐藏文件和常见的忽略目录
                    if (entry.startsWith('.') && !entry.startsWith('.git')) {
                        continue;
                    }
                    if (['node_modules', 'dist', 'build', '.git'].includes(entry)) {
                        continue;
                    }
                    const entryPath = path.join(dirPath, entry);
                    try {
                        const childNode = await this.buildDirectoryTree(entryPath, currentDepth + 1, maxDepth);
                        children.push(childNode);
                    }
                    catch (error) {
                        // 跳过无法访问的文件/目录
                        continue;
                    }
                }
                // 排序：目录在前，文件在后，按名称排序
                children.sort((a, b) => {
                    if (a.type !== b.type) {
                        return a.type === 'directory' ? -1 : 1;
                    }
                    return a.name.localeCompare(b.name);
                });
                node.children = children;
            }
            catch (error) {
                // 无法读取目录内容，可能是权限问题
                this.logger.warn(`无法读取目录: ${dirPath}`);
            }
        }
        return node;
    }
    /**
     * 在目录中搜索
     */
    async searchInDirectory(dirPath, regex, results, currentDepth, maxDepth) {
        if (currentDepth >= maxDepth)
            return;
        try {
            const entries = await fs.promises.readdir(dirPath);
            for (const entry of entries) {
                if (entry.startsWith('.') || ['node_modules', 'dist', 'build'].includes(entry)) {
                    continue;
                }
                const entryPath = path.join(dirPath, entry);
                const stats = await fs.promises.stat(entryPath);
                if (regex.test(entry)) {
                    results.push({
                        name: entry,
                        path: entryPath,
                        type: stats.isDirectory() ? 'directory' : 'file',
                        size: stats.size,
                        modified: stats.mtime,
                        extension: stats.isFile() ? path.extname(entry) : undefined,
                    });
                }
                if (stats.isDirectory()) {
                    await this.searchInDirectory(entryPath, regex, results, currentDepth + 1, maxDepth);
                }
            }
        }
        catch (error) {
            // 跳过无法访问的目录
        }
    }
    /**
     * 加载.gitignore规则
     */
    async loadGitIgnorePatterns(rootPath) {
        try {
            const gitignorePath = path.join(rootPath, '.gitignore');
            const content = await fs.promises.readFile(gitignorePath, 'utf-8');
            const patterns = content
                .split('\n')
                .map(line => line.trim())
                .filter(line => line && !line.startsWith('#'));
            this.gitIgnorePatterns.set(rootPath, patterns);
        }
        catch (error) {
            // .gitignore文件不存在或无法读取
            this.gitIgnorePatterns.set(rootPath, []);
        }
    }
    /**
     * 检查文件是否被git忽略
     */
    isGitIgnored(filePath) {
        // 简化的gitignore检查，实际实现可以更复杂
        const fileName = path.basename(filePath);
        const commonIgnored = [
            'node_modules',
            '.DS_Store',
            'Thumbs.db',
            '*.log',
            'dist',
            'build',
            '.env',
        ];
        return commonIgnored.some(pattern => {
            if (pattern.includes('*')) {
                const regex = new RegExp(pattern.replace(/\*/g, '.*'));
                return regex.test(fileName);
            }
            return fileName === pattern;
        });
    }
    /**
     * 清理资源
     */
    async cleanup() {
        for (const [dirPath, watcher] of this.watchers) {
            watcher.close();
        }
        this.watchers.clear();
        this.logger.info('文件管理服务已清理');
    }
};
exports.FileManagerService = FileManagerService;
exports.FileManagerService = FileManagerService = __decorate([
    (0, inversify_1.injectable)(),
    __param(0, (0, inversify_1.inject)(types_1.TYPES.Logger)),
    __param(1, (0, inversify_1.inject)(types_1.TYPES.EventBus)),
    __metadata("design:paramtypes", [Object, event_bus_1.EventBus])
], FileManagerService);
