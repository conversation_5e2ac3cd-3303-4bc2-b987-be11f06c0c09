/**
 * 增强版Web界面功能测试
 * 测试所有API端点和功能
 */

async function testEnhancedWebAPI() {
  console.log('🧪 开始测试增强版Web界面API...\n');

  const baseUrl = 'http://localhost:3334';
  
  try {
    // 测试1: 系统状态
    console.log('1️⃣ 测试系统状态API...');
    const statusResponse = await fetch(`${baseUrl}/api/status`);
    const statusData = await statusResponse.json();
    console.log('✅ 系统状态:', statusData.data.systemStatus);
    console.log(`   项目数量: ${statusData.data.projectCount}`);
    console.log(`   运行时间: ${Math.floor(statusData.data.uptime)}秒`);

    // 测试2: 项目扫描
    console.log('\n2️⃣ 测试项目扫描API...');
    const scanResponse = await fetch(`${baseUrl}/api/projects/scan`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ path: '.' })
    });
    const scanData = await scanResponse.json();
    console.log(`✅ 扫描完成，发现 ${scanData.data.length} 个项目`);

    // 测试3: 获取项目列表
    console.log('\n3️⃣ 测试项目列表API...');
    const projectsResponse = await fetch(`${baseUrl}/api/projects`);
    const projectsData = await projectsResponse.json();
    console.log(`✅ 项目列表获取成功，共 ${projectsData.data.length} 个项目`);

    // 测试4: 文件树API
    console.log('\n4️⃣ 测试文件树API...');
    const fileTreeResponse = await fetch(`${baseUrl}/api/files/tree?path=.&depth=2`);
    const fileTreeData = await fileTreeResponse.json();
    console.log('✅ 文件树获取成功');
    console.log(`   根目录: ${fileTreeData.data.name}`);
    console.log(`   子项数量: ${fileTreeData.data.children?.length || 0}`);

    // 测试5: 文件搜索API
    console.log('\n5️⃣ 测试文件搜索API...');
    const searchResponse = await fetch(`${baseUrl}/api/files/search?pattern=package.json`);
    const searchData = await searchResponse.json();
    console.log(`✅ 文件搜索完成，找到 ${searchData.data.length} 个匹配文件`);

    // 测试6: 工作流API
    console.log('\n6️⃣ 测试工作流API...');
    const workflowResponse = await fetch(`${baseUrl}/api/workflows`);
    const workflowData = await workflowResponse.json();
    console.log(`✅ 工作流列表获取成功，共 ${workflowData.data.length} 个工作流组`);

    // 测试7: n8n集成API
    console.log('\n7️⃣ 测试n8n集成API...');
    const n8nResponse = await fetch(`${baseUrl}/api/n8n/projects`);
    const n8nData = await n8nResponse.json();
    console.log(`✅ n8n格式项目数据获取成功，共 ${n8nData.data.length} 个项目`);
    if (n8nData.data.length > 0) {
      const firstProject = n8nData.data[0];
      console.log(`   示例项目: ${firstProject.name} (${firstProject.type})`);
      console.log(`   Git信息: ${firstProject.git ? firstProject.git.branch : '无'}`);
    }

    // 测试8: API文档
    console.log('\n8️⃣ 测试API文档...');
    const docsResponse = await fetch(`${baseUrl}/api/docs`);
    const docsData = await docsResponse.json();
    console.log('✅ API文档获取成功');
    console.log(`   API版本: ${docsData.version}`);
    console.log(`   端点数量: ${Object.keys(docsData.endpoints).length}`);

    // 测试9: 批量操作API（模拟）
    console.log('\n9️⃣ 测试批量操作API...');
    if (projectsData.data.length > 0) {
      const projectIds = projectsData.data.slice(0, 2).map((p: any) => p.id);
      const batchResponse = await fetch(`${baseUrl}/api/batch/git-status`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ projectIds })
      });
      const batchData = await batchResponse.json();
      console.log('✅ 批量操作测试完成');
      console.log(`   操作类型: Git Status`);
      console.log(`   成功: ${batchData.data.successCount}, 失败: ${batchData.data.failureCount}`);
    } else {
      console.log('⚠️  跳过批量操作测试（无项目）');
    }

    // 测试10: 文件内容API
    console.log('\n🔟 测试文件内容API...');
    try {
      const contentResponse = await fetch(`${baseUrl}/api/files/content?path=package.json`);
      const contentData = await contentResponse.json();
      if (contentData.success) {
        console.log('✅ 文件内容获取成功');
        console.log(`   文件大小: ${contentData.data.content.length} 字符`);
      } else {
        console.log('⚠️  文件内容获取失败:', contentData.error);
      }
    } catch (error) {
      console.log('⚠️  文件内容测试跳过（文件不存在）');
    }

    console.log('\n🎉 所有API测试完成！');
    console.log('\n📊 测试总结:');
    console.log('✅ 系统状态API - 正常');
    console.log('✅ 项目管理API - 正常');
    console.log('✅ 文件管理API - 正常');
    console.log('✅ 工作流API - 正常');
    console.log('✅ 批量操作API - 正常');
    console.log('✅ n8n集成API - 正常');
    console.log('✅ API文档 - 正常');

    console.log('\n🌐 Web界面功能:');
    console.log('📦 项目管理 - 多选、批量操作');
    console.log('📁 文件浏览器 - 树形结构、搜索、预览');
    console.log('🔄 工作流管理 - 可视化编辑');
    console.log('📊 批量操作 - 并发执行');
    console.log('🔍 实时监控 - 状态监控');
    console.log('🔗 n8n集成 - 完整支持');

    console.log('\n🚀 增强版Web界面测试成功！');
    console.log(`📍 访问地址: ${baseUrl}`);
    console.log(`🔧 API端点: ${baseUrl}/api`);
    console.log(`📚 API文档: ${baseUrl}/api/docs`);

  } catch (error) {
    console.error('\n❌ 测试失败:', error);
  }
}

// 运行测试
if (require.main === module) {
  testEnhancedWebAPI().catch(console.error);
}

export { testEnhancedWebAPI };
