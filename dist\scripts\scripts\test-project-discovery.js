"use strict";
/**
 * 项目发现功能测试脚本
 * 测试项目扫描和管理功能
 */
Object.defineProperty(exports, "__esModule", { value: true });
require("reflect-metadata");
const inversify_1 = require("inversify");
const event_bus_1 = require("../src/main/core/event-bus");
const logger_simple_service_1 = require("../src/main/services/logger-simple.service");
const state_simple_service_1 = require("../src/main/services/state-simple.service");
const git_adapter_1 = require("../src/main/plugins/git/git-adapter");
const project_discovery_service_1 = require("../src/main/services/project-discovery.service");
const types_1 = require("../src/shared/types");
async function testProjectDiscovery() {
    console.log('🔍 开始测试项目发现功能...\n');
    try {
        // 创建依赖注入容器
        const container = new inversify_1.Container();
        // 注册服务
        container.bind(types_1.TYPES.EventBus).to(event_bus_1.EventBus).inSingletonScope();
        container.bind(types_1.TYPES.Logger).to(logger_simple_service_1.SimpleLogger).inSingletonScope();
        container.bind(types_1.TYPES.StateRepository).to(state_simple_service_1.SimpleStateRepository).inSingletonScope();
        container.bind(types_1.TYPES.VersionControlAdapter).to(git_adapter_1.GitAdapter).inSingletonScope();
        container.bind(types_1.TYPES.ProjectDiscoveryService).to(project_discovery_service_1.ProjectDiscoveryService).inSingletonScope();
        console.log('✅ 依赖注入容器创建成功');
        // 获取服务实例
        const eventBus = container.get(types_1.TYPES.EventBus);
        const projectService = container.get(types_1.TYPES.ProjectDiscoveryService);
        // 监听项目发现事件
        let discoveredProjects = 0;
        eventBus.on(types_1.CoreEvents.PROJECT_DISCOVERED, (event) => {
            discoveredProjects++;
            console.log(`📦 发现项目: ${event.payload.name} (${event.payload.type})`);
        });
        // 测试扫描当前目录
        console.log('\n🔍 扫描当前目录...');
        const projects = await projectService.scanDirectory('.');
        console.log(`\n📊 扫描结果:`);
        console.log(`- 发现项目数量: ${projects.length}`);
        console.log(`- 事件触发次数: ${discoveredProjects}`);
        if (projects.length > 0) {
            const project = projects[0];
            console.log(`\n📋 项目详情:`);
            console.log(`- 名称: ${project.name}`);
            console.log(`- 类型: ${project.type}`);
            console.log(`- 路径: ${project.path}`);
            if (project.gitInfo) {
                console.log(`- Git分支: ${project.gitInfo.branch}`);
                console.log(`- 有未提交更改: ${project.gitInfo.hasUncommittedChanges}`);
                console.log(`- 有未跟踪文件: ${project.gitInfo.hasUntrackedFiles}`);
            }
            // 测试项目刷新
            console.log('\n🔄 测试项目刷新...');
            await projectService.refreshProject(project.id);
            console.log('✅ 项目刷新成功');
            // 测试获取所有项目
            console.log('\n📋 获取所有项目...');
            const allProjects = projectService.getProjects();
            console.log(`项目总数: ${allProjects.length}`);
        }
        console.log('\n🎉 项目发现功能测试通过！');
    }
    catch (error) {
        console.error('\n❌ 测试失败:', error);
        process.exit(1);
    }
}
// 运行测试
testProjectDiscovery().catch(console.error);
