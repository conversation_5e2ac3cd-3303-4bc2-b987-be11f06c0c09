"use strict";
/**
 * Git适配器实现
 * 基于 simple-git 的版本控制操作
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GitAdapter = void 0;
const inversify_1 = require("inversify");
const simple_git_1 = require("simple-git");
const path = __importStar(require("path"));
const types_1 = require("../../../shared/types");
const utils_1 = require("../../../shared/utils");
let GitAdapter = class GitAdapter {
    constructor(logger) {
        this.logger = logger;
    }
    /**
     * 检查路径是否是Git仓库
     */
    async isRepository(repoPath) {
        try {
            const gitDir = path.join(repoPath, '.git');
            return await (0, utils_1.fileExists)(gitDir);
        }
        catch (error) {
            this.logger.debug(`检查Git仓库失败: ${repoPath}`, error);
            return false;
        }
    }
    /**
     * 获取Git状态
     */
    async getStatus(repoPath) {
        try {
            const git = (0, simple_git_1.simpleGit)(repoPath);
            // 获取当前分支
            const branch = await this.getCurrentBranch(repoPath);
            // 获取状态信息
            const status = await git.status();
            // 获取远程分支信息
            let ahead = 0;
            let behind = 0;
            try {
                // 尝试获取与远程分支的差异
                const remoteBranch = `origin/${branch}`;
                const aheadBehind = await git.raw(['rev-list', '--left-right', '--count', `${remoteBranch}...HEAD`]);
                const [behindCount, aheadCount] = aheadBehind.trim().split('\t').map(Number);
                ahead = aheadCount || 0;
                behind = behindCount || 0;
            }
            catch (error) {
                this.logger.debug(`获取远程分支差异失败: ${repoPath}`, error);
            }
            return {
                branch,
                ahead,
                behind,
                hasUncommittedChanges: status.files.length > 0,
                hasUntrackedFiles: status.not_added.length > 0,
            };
        }
        catch (error) {
            this.logger.error(`获取Git状态失败: ${repoPath}`, error);
            throw error;
        }
    }
    /**
     * 执行git pull
     */
    async pull(repoPath) {
        try {
            this.logger.info(`执行git pull: ${repoPath}`);
            const git = (0, simple_git_1.simpleGit)(repoPath);
            await git.pull();
            this.logger.info(`git pull完成: ${repoPath}`);
        }
        catch (error) {
            this.logger.error(`git pull失败: ${repoPath}`, error);
            throw error;
        }
    }
    /**
     * 获取当前分支
     */
    async getCurrentBranch(repoPath) {
        try {
            const git = (0, simple_git_1.simpleGit)(repoPath);
            const branch = await git.revparse(['--abbrev-ref', 'HEAD']);
            return branch.trim();
        }
        catch (error) {
            this.logger.error(`获取当前分支失败: ${repoPath}`, error);
            return 'unknown';
        }
    }
    /**
     * 获取远程仓库URL
     */
    async getRemoteUrl(repoPath) {
        try {
            const git = (0, simple_git_1.simpleGit)(repoPath);
            const remotes = await git.getRemotes(true);
            const origin = remotes.find(remote => remote.name === 'origin');
            return origin?.refs?.fetch || null;
        }
        catch (error) {
            this.logger.debug(`获取远程URL失败: ${repoPath}`, error);
            return null;
        }
    }
    /**
     * 获取最近的提交信息
     */
    async getLatestCommit(repoPath) {
        try {
            const git = (0, simple_git_1.simpleGit)(repoPath);
            const log = await git.log({ maxCount: 1 });
            return log.latest;
        }
        catch (error) {
            this.logger.error(`获取最近提交失败: ${repoPath}`, error);
            return null;
        }
    }
    /**
     * 检查是否有未提交的更改
     */
    async hasUncommittedChanges(repoPath) {
        try {
            const git = (0, simple_git_1.simpleGit)(repoPath);
            const status = await git.status();
            return status.files.length > 0;
        }
        catch (error) {
            this.logger.error(`检查未提交更改失败: ${repoPath}`, error);
            return false;
        }
    }
    /**
     * 获取分支列表
     */
    async getBranches(repoPath) {
        try {
            const git = (0, simple_git_1.simpleGit)(repoPath);
            const branches = await git.branchLocal();
            return branches.all;
        }
        catch (error) {
            this.logger.error(`获取分支列表失败: ${repoPath}`, error);
            return [];
        }
    }
    /**
     * 切换分支
     */
    async checkout(repoPath, branch) {
        try {
            this.logger.info(`切换分支: ${repoPath} -> ${branch}`);
            const git = (0, simple_git_1.simpleGit)(repoPath);
            await git.checkout(branch);
            this.logger.info(`分支切换完成: ${branch}`);
        }
        catch (error) {
            this.logger.error(`切换分支失败: ${repoPath} -> ${branch}`, error);
            throw error;
        }
    }
    /**
     * 获取提交历史
     */
    async getCommitHistory(repoPath, maxCount = 10) {
        try {
            const git = (0, simple_git_1.simpleGit)(repoPath);
            const log = await git.log({ maxCount });
            return [...log.all];
        }
        catch (error) {
            this.logger.error(`获取提交历史失败: ${repoPath}`, error);
            return [];
        }
    }
};
exports.GitAdapter = GitAdapter;
exports.GitAdapter = GitAdapter = __decorate([
    (0, inversify_1.injectable)(),
    __param(0, (0, inversify_1.inject)(types_1.TYPES.Logger)),
    __metadata("design:paramtypes", [Object])
], GitAdapter);
