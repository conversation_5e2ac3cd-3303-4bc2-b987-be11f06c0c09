"use strict";
/**
 * 事件总线实现
 * 基于 EventEmitter2 实现的事件驱动架构核心
 */
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventBus = void 0;
const eventemitter2_1 = require("eventemitter2");
const inversify_1 = require("inversify");
let EventBus = class EventBus {
    constructor() {
        this.emitter = new eventemitter2_1.EventEmitter2({
            wildcard: true,
            delimiter: '.',
            newListener: false,
            maxListeners: 20,
            verboseMemoryLeak: true,
        });
    }
    /**
     * 发射事件
     */
    emit(event) {
        this.emitter.emit(event.type, event);
    }
    /**
     * 监听事件
     */
    on(eventType, handler) {
        this.emitter.on(eventType, handler);
    }
    /**
     * 取消监听事件
     */
    off(eventType, handler) {
        this.emitter.off(eventType, handler);
    }
    /**
     * 一次性监听事件
     */
    once(eventType, handler) {
        this.emitter.once(eventType, handler);
    }
    /**
     * 获取事件监听器数量
     */
    listenerCount(eventType) {
        return this.emitter.listenerCount(eventType);
    }
    /**
     * 移除所有监听器
     */
    removeAllListeners(eventType) {
        this.emitter.removeAllListeners(eventType);
    }
    /**
     * 获取所有事件名称
     */
    eventNames() {
        return this.emitter.eventNames();
    }
    /**
     * 创建领域事件
     */
    createEvent(type, payload) {
        return {
            type,
            timestamp: Date.now(),
            payload,
        };
    }
    /**
     * 发射领域事件（便捷方法）
     */
    emitEvent(type, payload) {
        const event = this.createEvent(type, payload);
        this.emit(event);
    }
};
exports.EventBus = EventBus;
exports.EventBus = EventBus = __decorate([
    (0, inversify_1.injectable)(),
    __metadata("design:paramtypes", [])
], EventBus);
