"use strict";
/**
 * 服务编排器
 * 处理服务依赖关系、启动顺序和生命周期管理
 */
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServiceOrchestrator = exports.ServiceStatus = exports.WorkflowStatus = void 0;
const inversify_1 = require("inversify");
const types_1 = require("../../shared/types");
const utils_1 = require("../../shared/utils");
const event_bus_1 = require("../core/event-bus");
var WorkflowStatus;
(function (WorkflowStatus) {
    WorkflowStatus["STARTING"] = "starting";
    WorkflowStatus["RUNNING"] = "running";
    WorkflowStatus["STOPPING"] = "stopping";
    WorkflowStatus["STOPPED"] = "stopped";
    WorkflowStatus["FAILED"] = "failed";
})(WorkflowStatus || (exports.WorkflowStatus = WorkflowStatus = {}));
var ServiceStatus;
(function (ServiceStatus) {
    ServiceStatus["PENDING"] = "pending";
    ServiceStatus["STARTING"] = "starting";
    ServiceStatus["RUNNING"] = "running";
    ServiceStatus["STOPPING"] = "stopping";
    ServiceStatus["STOPPED"] = "stopped";
    ServiceStatus["FAILED"] = "failed";
    ServiceStatus["RESTARTING"] = "restarting";
})(ServiceStatus || (exports.ServiceStatus = ServiceStatus = {}));
let ServiceOrchestrator = class ServiceOrchestrator {
    constructor(logger, processLauncher, portMonitor, eventBus) {
        this.logger = logger;
        this.processLauncher = processLauncher;
        this.portMonitor = portMonitor;
        this.eventBus = eventBus;
        this.executions = new Map();
        this.executionCounter = 0;
        this.setupEventListeners();
    }
    /**
     * 启动工作流组
     */
    async startWorkflowGroup(group) {
        const executionId = `execution_${++this.executionCounter}_${Date.now()}`;
        const execution = {
            id: executionId,
            groupId: group.id,
            groupName: group.name,
            status: WorkflowStatus.STARTING,
            services: group.services.map(service => ({
                serviceId: service.id,
                serviceName: service.name,
                status: ServiceStatus.PENDING,
                restartCount: 0,
            })),
            startedAt: new Date(),
        };
        this.executions.set(executionId, execution);
        this.logger.info(`开始启动工作流组: ${group.name} (${executionId})`);
        try {
            // 新增：首先根据 orderIndex 对服务进行排序
            const sortedServices = [...group.services].sort((a, b) => a.orderIndex - b.orderIndex);
            // 使用排序后的服务列表计算启动顺序
            const startupOrder = this.calculateStartupOrder(sortedServices);
            this.logger.debug(`启动顺序: ${startupOrder.map(batch => batch.map(s => s.name).join(', ')).join(' -> ')}`);
            // 按批次启动服务
            for (const batch of startupOrder) {
                await this.startServiceBatch(execution, batch);
            }
            execution.status = WorkflowStatus.RUNNING;
            execution.completedAt = new Date();
            this.eventBus.emitEvent(types_1.CoreEvents.PROCESS_STARTED, {
                type: 'workflow_started',
                executionId,
                groupName: group.name,
            });
            this.logger.info(`工作流组启动完成: ${group.name}`);
        }
        catch (error) {
            execution.status = WorkflowStatus.FAILED;
            execution.error = error.message;
            execution.completedAt = new Date();
            this.logger.error(`工作流组启动失败: ${group.name}`, error);
            throw error;
        }
        return execution;
    }
    /**
     * 停止工作流组
     */
    async stopWorkflowGroup(executionId) {
        const execution = this.executions.get(executionId);
        if (!execution) {
            throw new Error(`工作流执行不存在: ${executionId}`);
        }
        this.logger.info(`开始停止工作流组: ${execution.groupName} (${executionId})`);
        execution.status = WorkflowStatus.STOPPING;
        try {
            // 按相反顺序停止服务
            const runningServices = execution.services
                .filter(s => s.status === ServiceStatus.RUNNING && s.processHandle)
                .reverse();
            for (const serviceExecution of runningServices) {
                await this.stopService(serviceExecution);
            }
            execution.status = WorkflowStatus.STOPPED;
            execution.completedAt = new Date();
            this.eventBus.emitEvent(types_1.CoreEvents.PROCESS_STOPPED, {
                type: 'workflow_stopped',
                executionId,
                groupName: execution.groupName,
            });
            this.logger.info(`工作流组已停止: ${execution.groupName}`);
        }
        catch (error) {
            execution.status = WorkflowStatus.FAILED;
            execution.error = error.message;
            this.logger.error(`停止工作流组失败: ${execution.groupName}`, error);
            throw error;
        }
    }
    /**
     * 重启服务
     */
    async restartService(executionId, serviceId) {
        const execution = this.executions.get(executionId);
        if (!execution) {
            throw new Error(`工作流执行不存在: ${executionId}`);
        }
        const serviceExecution = execution.services.find(s => s.serviceId === serviceId);
        if (!serviceExecution) {
            throw new Error(`服务不存在: ${serviceId}`);
        }
        this.logger.info(`重启服务: ${serviceExecution.serviceName}`);
        serviceExecution.status = ServiceStatus.RESTARTING;
        serviceExecution.restartCount++;
        try {
            // 停止现有进程
            if (serviceExecution.processHandle) {
                await this.processLauncher.stop(serviceExecution.processHandle);
            }
            // 等待一段时间
            await (0, utils_1.delay)(2000);
            // 重新启动服务
            // 注意：这里需要从原始配置重新获取服务配置
            // 为简化，这里假设可以从execution中获取
            this.logger.info(`服务重启完成: ${serviceExecution.serviceName}`);
        }
        catch (error) {
            serviceExecution.status = ServiceStatus.FAILED;
            serviceExecution.error = error.message;
            this.logger.error(`服务重启失败: ${serviceExecution.serviceName}`, error);
            throw error;
        }
    }
    /**
     * 获取工作流执行状态
     */
    getWorkflowExecution(executionId) {
        return this.executions.get(executionId) || null;
    }
    /**
     * 获取所有活动的执行
     */
    getActiveExecutions() {
        return Array.from(this.executions.values())
            .filter(e => e.status === WorkflowStatus.RUNNING || e.status === WorkflowStatus.STARTING);
    }
    /**
     * 停止所有执行
     */
    async stopAllExecutions() {
        const activeExecutions = this.getActiveExecutions();
        this.logger.info(`停止所有工作流执行，共 ${activeExecutions.length} 个`);
        const stopPromises = activeExecutions.map(execution => this.stopWorkflowGroup(execution.id).catch(error => this.logger.error(`停止工作流失败: ${execution.groupName}`, error)));
        await Promise.allSettled(stopPromises);
    }
    /**
     * 计算服务启动顺序
     */
    calculateStartupOrder(services) {
        const serviceMap = new Map(services.map(s => [s.id, s]));
        const visited = new Set();
        const visiting = new Set();
        const order = [];
        const levels = new Map();
        // 深度优先搜索计算依赖层级
        const dfs = (serviceId) => {
            if (visiting.has(serviceId)) {
                throw new Error(`检测到循环依赖: ${serviceId}`);
            }
            if (visited.has(serviceId)) {
                return levels.get(serviceId) || 0;
            }
            visiting.add(serviceId);
            const service = serviceMap.get(serviceId);
            if (!service) {
                throw new Error(`服务不存在: ${serviceId}`);
            }
            let maxDepLevel = -1;
            if (service.dependsOn) {
                for (const depId of service.dependsOn) {
                    const depLevel = dfs(depId);
                    maxDepLevel = Math.max(maxDepLevel, depLevel);
                }
            }
            const level = maxDepLevel + 1;
            levels.set(serviceId, level);
            visited.add(serviceId);
            visiting.delete(serviceId);
            return level;
        };
        // 计算所有服务的层级
        for (const service of services) {
            if (!visited.has(service.id)) {
                dfs(service.id);
            }
        }
        // 按层级分组
        const levelGroups = new Map();
        for (const service of services) {
            const level = levels.get(service.id) || 0;
            if (!levelGroups.has(level)) {
                levelGroups.set(level, []);
            }
            levelGroups.get(level).push(service);
        }
        // 按层级顺序返回
        const sortedLevels = Array.from(levelGroups.keys()).sort((a, b) => a - b);
        return sortedLevels.map(level => levelGroups.get(level));
    }
    /**
     * 启动服务批次
     */
    async startServiceBatch(execution, services) {
        const startPromises = services.map(service => this.startSingleService(execution, service));
        await Promise.all(startPromises);
    }
    /**
     * 启动单个服务
     */
    async startSingleService(execution, service) {
        const serviceExecution = execution.services.find(s => s.serviceId === service.id);
        if (!serviceExecution) {
            throw new Error(`服务执行状态不存在: ${service.id}`);
        }
        this.logger.info(`启动服务: ${service.name}`);
        serviceExecution.status = ServiceStatus.STARTING;
        serviceExecution.startedAt = new Date();
        try {
            // 检查端口是否可用
            if (service.port) {
                const portAvailable = await this.portMonitor.checkPort(service.port);
                if (!portAvailable) {
                    throw new Error(`端口 ${service.port} 已被占用`);
                }
            }
            // 创建进程配置
            const processConfig = {
                id: `${execution.id}_${service.id}`,
                name: `${execution.groupName}:${service.name}`,
                command: service.command,
                cwd: service.projectId, // 假设projectId是项目路径
                env: service.env,
            };
            // 启动进程
            const processHandle = await this.processLauncher.start(processConfig);
            serviceExecution.processHandle = processHandle;
            serviceExecution.status = ServiceStatus.RUNNING;
            this.logger.info(`服务启动成功: ${service.name} (PID: ${processHandle.pid})`);
            // 如果有健康检查URL，等待服务就绪
            if (service.healthCheckUrl) {
                await this.waitForServiceHealth(service);
            }
        }
        catch (error) {
            serviceExecution.status = ServiceStatus.FAILED;
            serviceExecution.error = error.message;
            this.logger.error(`服务启动失败: ${service.name}`, error);
            throw error;
        }
    }
    /**
     * 停止单个服务
     */
    async stopService(serviceExecution) {
        if (!serviceExecution.processHandle) {
            return;
        }
        this.logger.info(`停止服务: ${serviceExecution.serviceName}`);
        serviceExecution.status = ServiceStatus.STOPPING;
        try {
            await this.processLauncher.stop(serviceExecution.processHandle);
            serviceExecution.status = ServiceStatus.STOPPED;
            this.logger.info(`服务已停止: ${serviceExecution.serviceName}`);
        }
        catch (error) {
            serviceExecution.status = ServiceStatus.FAILED;
            serviceExecution.error = error.message;
            this.logger.error(`停止服务失败: ${serviceExecution.serviceName}`, error);
            throw error;
        }
    }
    /**
     * 等待服务健康检查
     */
    async waitForServiceHealth(service) {
        if (!service.healthCheckUrl)
            return;
        const maxAttempts = 30;
        const interval = 2000;
        for (let attempt = 1; attempt <= maxAttempts; attempt++) {
            try {
                // 这里应该实现HTTP健康检查
                // 为简化，假设总是成功
                this.logger.debug(`健康检查通过: ${service.name}`);
                return;
            }
            catch (error) {
                if (attempt === maxAttempts) {
                    throw new Error(`服务健康检查失败: ${service.name}`);
                }
                await (0, utils_1.delay)(interval);
            }
        }
    }
    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 监听进程退出事件，处理自动重启
        this.eventBus.on(types_1.CoreEvents.PROCESS_STOPPED, (event) => {
            this.handleProcessExit(event.payload);
        });
        this.eventBus.on(types_1.CoreEvents.PROCESS_CRASHED, (event) => {
            this.handleProcessCrash(event.payload);
        });
    }
    /**
     * 处理进程退出
     */
    handleProcessExit(payload) {
        // 查找对应的服务执行
        for (const execution of this.executions.values()) {
            const serviceExecution = execution.services.find(s => s.processHandle && s.processHandle.id === payload.processId);
            if (serviceExecution) {
                serviceExecution.status = ServiceStatus.STOPPED;
                this.logger.info(`服务进程退出: ${serviceExecution.serviceName}`);
                break;
            }
        }
    }
    /**
     * 处理进程崩溃
     */
    handleProcessCrash(payload) {
        // 查找对应的服务执行并处理自动重启
        for (const execution of this.executions.values()) {
            const serviceExecution = execution.services.find(s => s.processHandle && s.processHandle.id === payload.processId);
            if (serviceExecution) {
                serviceExecution.status = ServiceStatus.FAILED;
                serviceExecution.error = '进程崩溃';
                this.logger.error(`服务进程崩溃: ${serviceExecution.serviceName}`);
                // 这里可以实现自动重启逻辑
                break;
            }
        }
    }
};
exports.ServiceOrchestrator = ServiceOrchestrator;
exports.ServiceOrchestrator = ServiceOrchestrator = __decorate([
    (0, inversify_1.injectable)(),
    __param(0, (0, inversify_1.inject)(types_1.TYPES.Logger)),
    __param(1, (0, inversify_1.inject)(types_1.TYPES.ProcessLauncher)),
    __param(2, (0, inversify_1.inject)(types_1.TYPES.PortMonitor)),
    __param(3, (0, inversify_1.inject)(types_1.TYPES.EventBus)),
    __metadata("design:paramtypes", [Object, Object, Object, event_bus_1.EventBus])
], ServiceOrchestrator);
