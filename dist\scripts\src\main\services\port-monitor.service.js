"use strict";
/**
 * 端口监控服务实现
 * 监控指定端口的可用性状态
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PortMonitor = void 0;
const inversify_1 = require("inversify");
const net = __importStar(require("net"));
const types_1 = require("../../shared/types");
const event_bus_1 = require("../core/event-bus");
let PortMonitor = class PortMonitor {
    constructor(logger, eventBus) {
        this.logger = logger;
        this.eventBus = eventBus;
        this.watchers = new Map();
        this.CHECK_INTERVAL = 2000; // 2秒检查一次
    }
    /**
     * 监控端口状态
     */
    watch(port, callback) {
        this.logger.debug(`开始监控端口: ${port}`);
        // 立即检查一次端口状态
        this.checkPort(port).then(available => {
            callback(available);
        });
        // 创建定时检查
        const interval = setInterval(async () => {
            try {
                const available = await this.checkPort(port);
                const watchers = this.watchers.get(port) || [];
                const watcher = watchers.find(w => w.callback === callback);
                if (watcher && watcher.lastStatus !== available) {
                    watcher.lastStatus = available;
                    callback(available);
                    // 发射端口状态变化事件
                    this.eventBus.emitEvent(available ? types_1.CoreEvents.PORT_AVAILABLE : types_1.CoreEvents.PORT_UNAVAILABLE, { port, available });
                }
            }
            catch (error) {
                this.logger.error(`检查端口状态失败: ${port}`, error);
            }
        }, this.CHECK_INTERVAL);
        // 创建监听器对象
        const watcher = {
            port,
            callback,
            interval,
            lastStatus: false,
        };
        // 添加到监听器列表
        if (!this.watchers.has(port)) {
            this.watchers.set(port, []);
        }
        this.watchers.get(port).push(watcher);
        // 返回停止监听的方法
        return {
            stop: () => {
                this.stopWatcher(port, watcher);
            },
        };
    }
    /**
     * 检查端口是否可用
     */
    async checkPort(port) {
        return new Promise((resolve) => {
            const socket = new net.Socket();
            const timeout = setTimeout(() => {
                socket.destroy();
                resolve(false); // 超时认为端口不可用
            }, 1000);
            socket.connect(port, 'localhost', () => {
                clearTimeout(timeout);
                socket.destroy();
                resolve(true); // 连接成功，端口被占用
            });
            socket.on('error', () => {
                clearTimeout(timeout);
                resolve(false); // 连接失败，端口可用
            });
        });
    }
    /**
     * 停止监听器
     */
    stopWatcher(port, targetWatcher) {
        const watchers = this.watchers.get(port);
        if (!watchers)
            return;
        const index = watchers.findIndex(w => w === targetWatcher);
        if (index !== -1) {
            clearInterval(targetWatcher.interval);
            watchers.splice(index, 1);
            // 如果没有更多监听器，移除端口
            if (watchers.length === 0) {
                this.watchers.delete(port);
            }
            this.logger.debug(`停止监控端口: ${port}`);
        }
    }
    /**
     * 停止所有监听器
     */
    stopAll() {
        for (const [port, watchers] of this.watchers) {
            for (const watcher of watchers) {
                clearInterval(watcher.interval);
            }
        }
        this.watchers.clear();
        this.logger.info('已停止所有端口监控');
    }
    /**
     * 获取当前监控的端口列表
     */
    getWatchedPorts() {
        return Array.from(this.watchers.keys());
    }
    /**
     * 批量检查多个端口
     */
    async checkMultiplePorts(ports) {
        const results = new Map();
        const promises = ports.map(async (port) => {
            const available = await this.checkPort(port);
            results.set(port, available);
        });
        await Promise.all(promises);
        return results;
    }
};
exports.PortMonitor = PortMonitor;
exports.PortMonitor = PortMonitor = __decorate([
    (0, inversify_1.injectable)(),
    __param(0, (0, inversify_1.inject)(types_1.TYPES.Logger)),
    __param(1, (0, inversify_1.inject)(types_1.TYPES.EventBus)),
    __metadata("design:paramtypes", [Object, event_bus_1.EventBus])
], PortMonitor);
