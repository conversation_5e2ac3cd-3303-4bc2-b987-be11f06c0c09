"use strict";
/**
 * DevWorkbench MVP 演示脚本
 * 展示核心功能的完整工作流程
 */
Object.defineProperty(exports, "__esModule", { value: true });
require("reflect-metadata");
const inversify_1 = require("inversify");
const event_bus_1 = require("../src/main/core/event-bus");
const logger_simple_service_1 = require("../src/main/services/logger-simple.service");
const state_simple_service_1 = require("../src/main/services/state-simple.service");
const git_adapter_1 = require("../src/main/plugins/git/git-adapter");
const project_discovery_service_1 = require("../src/main/services/project-discovery.service");
const types_1 = require("../src/shared/types");
async function runDemo() {
    console.log('🚀 DevWorkbench MVP 演示\n');
    console.log('='.repeat(50));
    try {
        // 1. 初始化系统
        console.log('\n📦 初始化依赖注入容器...');
        const container = new inversify_1.Container();
        container.bind(types_1.TYPES.EventBus).to(event_bus_1.EventBus).inSingletonScope();
        container.bind(types_1.TYPES.Logger).to(logger_simple_service_1.SimpleLogger).inSingletonScope();
        container.bind(types_1.TYPES.StateRepository).to(state_simple_service_1.SimpleStateRepository).inSingletonScope();
        container.bind(types_1.TYPES.VersionControlAdapter).to(git_adapter_1.GitAdapter).inSingletonScope();
        container.bind(types_1.TYPES.ProjectDiscoveryService).to(project_discovery_service_1.ProjectDiscoveryService).inSingletonScope();
        const eventBus = container.get(types_1.TYPES.EventBus);
        const logger = container.get(types_1.TYPES.Logger);
        const stateRepository = container.get(types_1.TYPES.StateRepository);
        const gitAdapter = container.get(types_1.TYPES.VersionControlAdapter);
        const projectService = container.get(types_1.TYPES.ProjectDiscoveryService);
        console.log('✅ 系统初始化完成');
        // 2. 事件监听演示
        console.log('\n📡 设置事件监听器...');
        let eventCount = 0;
        eventBus.on(types_1.CoreEvents.PROJECT_DISCOVERED, (event) => {
            eventCount++;
            logger.info(`项目发现事件: ${event.payload.name}`);
        });
        eventBus.on(types_1.CoreEvents.PROJECT_UPDATED, (event) => {
            logger.info(`项目更新事件: ${event.payload.name}`);
        });
        console.log('✅ 事件监听器设置完成');
        // 3. 状态管理演示
        console.log('\n💾 演示状态管理...');
        await stateRepository.set('demo.settings', {
            theme: 'dark',
            scanPaths: ['.', '../'],
            lastScan: new Date().toISOString()
        });
        const settings = await stateRepository.get('demo.settings');
        console.log('保存的设置:', settings);
        console.log('✅ 状态管理演示完成');
        // 4. Git 功能演示
        console.log('\n🔧 演示Git功能...');
        const isRepo = await gitAdapter.isRepository('.');
        console.log(`当前目录是Git仓库: ${isRepo}`);
        if (isRepo) {
            const status = await gitAdapter.getStatus('.');
            console.log('Git状态:', {
                branch: status.branch,
                ahead: status.ahead,
                behind: status.behind,
                hasUncommittedChanges: status.hasUncommittedChanges,
                hasUntrackedFiles: status.hasUntrackedFiles
            });
            const currentBranch = await gitAdapter.getCurrentBranch('.');
            console.log(`当前分支: ${currentBranch}`);
        }
        console.log('✅ Git功能演示完成');
        // 5. 项目发现演示
        console.log('\n🔍 演示项目发现功能...');
        console.log('扫描当前目录...');
        const projects = await projectService.scanDirectory('.');
        console.log(`发现 ${projects.length} 个项目`);
        if (projects.length > 0) {
            const project = projects[0];
            console.log('\n项目信息:');
            console.log(`- ID: ${project.id}`);
            console.log(`- 名称: ${project.name}`);
            console.log(`- 类型: ${project.type}`);
            console.log(`- 路径: ${project.path}`);
            if (project.gitInfo) {
                console.log(`- Git分支: ${project.gitInfo.branch}`);
                console.log(`- 状态: ${project.gitInfo.hasUncommittedChanges ? '有未提交更改' : '工作区干净'}`);
            }
            // 演示项目刷新
            console.log('\n🔄 刷新项目信息...');
            await projectService.refreshProject(project.id);
            // 获取更新后的项目列表
            const updatedProjects = projectService.getProjects();
            console.log(`项目列表中共有 ${updatedProjects.length} 个项目`);
        }
        console.log('✅ 项目发现演示完成');
        // 6. 统计信息
        console.log('\n📊 演示统计信息:');
        console.log(`- 触发的事件数量: ${eventCount}`);
        console.log(`- 管理的项目数量: ${projectService.getProjects().length}`);
        console.log(`- 事件总线监听器数量: ${eventBus.eventNames().length}`);
        // 7. 模拟快捷操作
        console.log('\n⚡ 模拟快捷操作...');
        console.log('模拟操作: 在VS Code中打开项目');
        console.log('模拟操作: 在终端中打开项目');
        console.log('模拟操作: 在文件管理器中打开项目');
        console.log('模拟操作: 执行Git Pull');
        console.log('✅ 快捷操作模拟完成');
        console.log('\n' + '='.repeat(50));
        console.log('🎉 DevWorkbench MVP 演示完成！');
        console.log('\n✨ 核心功能验证:');
        console.log('- ✅ 依赖注入架构');
        console.log('- ✅ 事件驱动通信');
        console.log('- ✅ 状态持久化');
        console.log('- ✅ Git集成');
        console.log('- ✅ 项目发现与管理');
        console.log('- ✅ 日志记录');
        console.log('\n🚀 系统已准备好进行进一步开发！');
    }
    catch (error) {
        console.error('\n❌ 演示过程中发生错误:', error);
        process.exit(1);
    }
}
// 运行演示
runDemo().catch(console.error);
