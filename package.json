{"name": "dev-workbench-simple", "version": "0.1.0", "description": "DevWorkbench 简化测试版本", "main": "dist/main/index.js", "scripts": {"test": "echo \"Test passed\"", "build:main": "tsc -p tsconfig.main.json", "start": "node dist/main/index.js", "cli": "npx tsc scripts/cli.ts --outDir dist/scripts --target ES2020 --module CommonJS --moduleResolution node --esModuleInterop --experimentalDecorators --emitDecoratorMetadata && node dist/scripts/cli.js", "interactive": "npx tsc scripts/interactive.ts --outDir dist/scripts --target ES2020 --module CommonJS --moduleResolution node --esModuleInterop --experimentalDecorators --emitDecoratorMetadata && node dist/scripts/interactive.js", "web": "npx tsc scripts/web-server.ts --outDir dist/scripts --target ES2020 --module CommonJS --moduleResolution node --esModuleInterop --experimentalDecorators --emitDecoratorMetadata && node dist/scripts/web-server.js", "web-enhanced": "npx tsc scripts/web-enhanced.ts --outDir dist/scripts --target ES2020 --module CommonJS --moduleResolution node --esModuleInterop --experimentalDecorators --emitDecoratorMetadata && node dist/scripts/web-enhanced.js", "demo": "npx tsc scripts/demo.ts --outDir dist/scripts --target ES2020 --module CommonJS --moduleResolution node --esModuleInterop --experimentalDecorators --emitDecoratorMetadata && node dist/scripts/demo.js", "test-all": "npx tsc scripts/test-core.ts --outDir dist/scripts --target ES2020 --module CommonJS --moduleResolution node --esModuleInterop --experimentalDecorators --emitDecoratorMetadata && npx tsc scripts/test-project-discovery.ts --outDir dist/scripts --target ES2020 --module CommonJS --moduleResolution node --esModuleInterop --experimentalDecorators --emitDecoratorMetadata && npx tsc scripts/test-process-management.ts --outDir dist/scripts --target ES2020 --module CommonJS --moduleResolution node --esModuleInterop --experimentalDecorators --emitDecoratorMetadata && npx tsc scripts/test-port-monitoring.ts --outDir dist/scripts --target ES2020 --module CommonJS --moduleResolution node --esModuleInterop --experimentalDecorators --emitDecoratorMetadata && npx tsc scripts/test-workflow-management.ts --outDir dist/scripts --target ES2020 --module CommonJS --moduleResolution node --esModuleInterop --experimentalDecorators --emitDecoratorMetadata && npx tsc scripts/integration-test.ts --outDir dist/scripts --target ES2020 --module CommonJS --moduleResolution node --esModuleInterop --experimentalDecorators --emitDecoratorMetadata && node dist/scripts/test-core.js && node dist/scripts/test-project-discovery.js && node dist/scripts/test-process-management.js && node dist/scripts/test-port-monitoring.js && node dist/scripts/test-workflow-management.js && node dist/scripts/integration-test.js"}, "dependencies": {"@types/express": "^5.0.3", "electron-store": "^8.1.0", "eventemitter2": "^6.4.9", "express": "^5.1.0", "inversify": "^6.0.2", "reflect-metadata": "^0.2.2", "simple-git": "^3.20.0", "winston": "^3.11.0"}, "devDependencies": {"@types/node": "^20.9.0", "typescript": "^5.2.2"}}