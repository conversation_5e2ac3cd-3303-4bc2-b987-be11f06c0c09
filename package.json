{"name": "dev-workbench", "version": "1.0.0", "description": "一个现代化的项目管理和开发工具平台", "main": "dist/main/index.js", "scripts": {"build:main": "tsc -p tsconfig.main.json", "build:renderer": "vite build", "build:scripts": "tsc scripts/*.ts --outDir dist/scripts --target ES2020 --module CommonJS --moduleResolution node --esModuleInterop --experimentalDecorators --emitDecoratorMetadata", "build": "npm run build:main && npm run build:renderer && npm run build:scripts", "start": "npm run build && electron .", "dev": "vite", "web": "npm run build && node dist/scripts/web-server.js", "web-enhanced": "npm run build && node dist/scripts/web-enhanced.js", "test": "jest"}, "dependencies": {"electron-store": "^8.1.0", "eventemitter2": "^6.4.9", "express": "^4.17.1", "inversify": "^6.0.2", "reflect-metadata": "^0.2.2", "simple-git": "^3.20.0", "winston": "^3.11.0", "xterm": "^5.3.0", "xterm-addon-fit": "^0.8.0", "xterm-addon-web-links": "^0.9.0"}, "devDependencies": {"@types/express": "^4.17.21", "@types/node": "^20.9.0", "@vitejs/plugin-react": "^4.2.1", "electron": "^31.0.2", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.2.2", "vite": "^5.1.4"}}