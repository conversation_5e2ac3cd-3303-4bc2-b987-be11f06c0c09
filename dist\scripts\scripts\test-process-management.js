"use strict";
/**
 * 进程管理功能测试脚本
 * 测试进程启动、监控和控制功能
 */
Object.defineProperty(exports, "__esModule", { value: true });
require("reflect-metadata");
const inversify_1 = require("inversify");
const event_bus_1 = require("../src/main/core/event-bus");
const logger_simple_service_1 = require("../src/main/services/logger-simple.service");
const process_launcher_service_1 = require("../src/main/services/process-launcher.service");
const types_1 = require("../src/shared/types");
async function testProcessManagement() {
    console.log('⚙️ 开始测试进程管理功能...\n');
    try {
        // 创建依赖注入容器
        const container = new inversify_1.Container();
        container.bind(types_1.TYPES.EventBus).to(event_bus_1.EventBus).inSingletonScope();
        container.bind(types_1.TYPES.Logger).to(logger_simple_service_1.SimpleLogger).inSingletonScope();
        container.bind(types_1.TYPES.ProcessLauncher).to(process_launcher_service_1.ProcessLauncher).inSingletonScope();
        const eventBus = container.get(types_1.TYPES.EventBus);
        const processLauncher = container.get(types_1.TYPES.ProcessLauncher);
        console.log('✅ 依赖注入容器创建成功');
        // 监听进程事件
        let processEvents = [];
        eventBus.on(types_1.CoreEvents.PROCESS_STARTED, (event) => {
            processEvents.push('STARTED');
            console.log(`🚀 进程启动: ${event.payload.config.name}`);
        });
        eventBus.on(types_1.CoreEvents.PROCESS_OUTPUT, (event) => {
            console.log(`📝 进程输出 [${event.payload.type}]: ${event.payload.data.trim()}`);
        });
        eventBus.on(types_1.CoreEvents.PROCESS_STOPPED, (event) => {
            processEvents.push('STOPPED');
            console.log(`⏹️ 进程停止: 退出码 ${event.payload.exitCode}`);
        });
        eventBus.on(types_1.CoreEvents.PROCESS_ERROR, (event) => {
            processEvents.push('ERROR');
            console.log(`❌ 进程错误: ${event.payload.error}`);
        });
        console.log('✅ 事件监听器设置完成');
        // 测试1: 启动简单命令
        console.log('\n🧪 测试1: 启动简单命令...');
        const config1 = {
            id: 'test-echo',
            name: 'Echo Test',
            command: 'echo',
            args: ['Hello from DevWorkbench!'],
            cwd: process.cwd(),
        };
        const handle1 = await processLauncher.start(config1);
        console.log(`进程启动成功: PID ${handle1.pid}, 状态: ${handle1.status}`);
        // 等待进程完成
        await new Promise(resolve => setTimeout(resolve, 2000));
        // 测试2: 启动长时间运行的命令
        console.log('\n🧪 测试2: 启动长时间运行的命令...');
        const config2 = {
            id: 'test-ping',
            name: 'Ping Test',
            command: process.platform === 'win32' ? 'ping' : 'ping',
            args: process.platform === 'win32' ? ['-n', '3', 'localhost'] : ['-c', '3', 'localhost'],
            cwd: process.cwd(),
        };
        const handle2 = await processLauncher.start(config2);
        console.log(`长时间进程启动: PID ${handle2.pid}, 状态: ${handle2.status}`);
        // 等待一段时间
        await new Promise(resolve => setTimeout(resolve, 3000));
        // 测试3: 获取运行中的进程
        console.log('\n🧪 测试3: 获取运行中的进程...');
        const runningProcesses = processLauncher.getRunningProcesses();
        console.log(`当前运行中的进程数量: ${runningProcesses.length}`);
        runningProcesses.forEach(process => {
            console.log(`- ${process.config.name} (PID: ${process.pid}, 状态: ${process.status})`);
        });
        // 测试4: 停止进程
        if (runningProcesses.length > 0) {
            console.log('\n🧪 测试4: 停止进程...');
            const processToStop = runningProcesses[0];
            console.log(`停止进程: ${processToStop.config.name}`);
            await processLauncher.stop(processToStop);
            // 等待停止完成
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
        // 测试5: 停止所有进程
        console.log('\n🧪 测试5: 停止所有进程...');
        await processLauncher.stopAll();
        console.log('所有进程已停止');
        // 等待清理完成
        await new Promise(resolve => setTimeout(resolve, 1000));
        // 最终检查
        const finalProcesses = processLauncher.getRunningProcesses();
        console.log(`最终运行中的进程数量: ${finalProcesses.length}`);
        // 统计信息
        console.log('\n📊 测试统计:');
        console.log(`- 触发的进程事件: ${processEvents.join(', ')}`);
        console.log(`- 事件总数: ${processEvents.length}`);
        console.log('\n🎉 进程管理功能测试完成！');
    }
    catch (error) {
        console.error('\n❌ 测试失败:', error);
        process.exit(1);
    }
}
// 运行测试
testProcessManagement().catch(console.error);
