/**
 * 项目清理验证脚本
 * 验证清理后的项目结构和功能是否正常
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 开始验证项目清理结果...\n');

let passedTests = 0;
let totalTests = 0;

function test(description, testFn) {
  totalTests++;
  try {
    testFn();
    console.log(`✅ ${description}`);
    passedTests++;
  } catch (error) {
    console.log(`❌ ${description}`);
    console.log(`   错误: ${error.message}`);
  }
}

function expect(actual) {
  return {
    toBe: (expected) => {
      if (actual !== expected) {
        throw new Error(`期望 ${expected}，但得到 ${actual}`);
      }
    },
    toContain: (expected) => {
      if (!actual.includes(expected)) {
        throw new Error(`期望包含 ${expected}，但在 ${actual} 中未找到`);
      }
    },
    toHaveLength: (expected) => {
      if (actual.length !== expected) {
        throw new Error(`期望长度为 ${expected}，但得到 ${actual.length}`);
      }
    },
    toMatch: (pattern) => {
      if (!pattern.test(actual)) {
        throw new Error(`期望匹配模式 ${pattern}，但 ${actual} 不匹配`);
      }
    },
    not: {
      toContain: (expected) => {
        if (actual.includes(expected)) {
          throw new Error(`期望不包含 ${expected}，但在 ${actual} 中找到了`);
        }
      },
      toMatch: (pattern) => {
        if (pattern.test(actual)) {
          throw new Error(`期望不匹配模式 ${pattern}，但 ${actual} 匹配了`);
        }
      }
    }
  };
}

// 测试 1: scripts 目录结构
console.log('📁 验证 scripts 目录结构...');

test('scripts 目录应该存在', () => {
  const scriptsDir = path.join(process.cwd(), 'scripts');
  expect(fs.existsSync(scriptsDir)).toBe(true);
  expect(fs.statSync(scriptsDir).isDirectory()).toBe(true);
});

test('应该包含所有预期的脚本文件', () => {
  const scriptsDir = path.join(process.cwd(), 'scripts');
  const expectedFiles = [
    'cli.ts', 'demo.ts', 'integration-test.ts', 'interactive.ts',
    'test-core.ts', 'test-port-monitoring.ts', 'test-process-management.ts',
    'test-project-discovery.ts', 'test-web-enhanced.ts', 'test-workflow-management.ts',
    'web-enhanced.ts', 'web-server-enhanced.ts', 'web-server.ts'
  ];

  const actualFiles = fs.readdirSync(scriptsDir).filter(file => file.endsWith('.ts'));

  expectedFiles.forEach(expectedFile => {
    expect(actualFiles).toContain(expectedFile);
  });
});

// 测试 2: 路径引用修复
console.log('\n🔗 验证路径引用修复...');

test('脚本文件应该使用正确的相对路径引用', () => {
  const scriptsDir = path.join(process.cwd(), 'scripts');
  const scriptFiles = fs.readdirSync(scriptsDir).filter(file => file.endsWith('.ts'));

  scriptFiles.forEach(scriptFile => {
    const filePath = path.join(scriptsDir, scriptFile);
    const content = fs.readFileSync(filePath, 'utf8');

    // 检查是否有错误的路径引用
    expect(content).not.toMatch(/from '\.\/src\//);
    expect(content).not.toMatch(/import '\.\/src\//);
  });
});

// 测试 3: 根目录清理
console.log('\n🧹 验证根目录清理...');

test('根目录不应该包含编译后的 .js 文件', () => {
  const rootFiles = fs.readdirSync(process.cwd());
  const jsFiles = rootFiles.filter(file =>
    file.endsWith('.js') &&
    !file.includes('jest.config') &&
    !file.includes('vite.config') &&
    !file.includes('.eslintrc') &&
    !file.includes('.prettierrc')
  );

  expect(jsFiles).toHaveLength(0);
});

test('根目录不应该包含临时 HTML 文件', () => {
  const rootFiles = fs.readdirSync(process.cwd());
  const htmlFiles = rootFiles.filter(file =>
    file.endsWith('.html') &&
    (file.includes('test-') || file.includes('enhanced-') || file.includes('working-'))
  );

  expect(htmlFiles).toHaveLength(0);
});

test('根目录不应该包含临时配置文件', () => {
  const rootFiles = fs.readdirSync(process.cwd());

  expect(rootFiles).not.toContain('package-simple.json');
  expect(rootFiles).not.toContain('test-state.json');
});

// 测试 4: 源码目录清理
console.log('\n📂 验证源码目录清理...');

test('src 目录不应该包含编译后的 .js 文件', () => {
  const srcDir = path.join(process.cwd(), 'src');

  function checkDirectory(dir) {
    if (!fs.existsSync(dir)) return;

    const files = fs.readdirSync(dir);

    files.forEach(file => {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);

      if (stat.isDirectory()) {
        checkDirectory(filePath);
      } else if (file.endsWith('.js')) {
        throw new Error(`发现编译产物文件: ${filePath}`);
      }
    });
  }

  checkDirectory(srcDir);
});

// 测试 5: .gitignore 文件
console.log('\n🚫 验证 .gitignore 文件...');

test('.gitignore 文件应该存在', () => {
  const gitignorePath = path.join(process.cwd(), '.gitignore');
  expect(fs.existsSync(gitignorePath)).toBe(true);
});

test('.gitignore 应该包含必要的忽略规则', () => {
  const gitignorePath = path.join(process.cwd(), '.gitignore');
  const content = fs.readFileSync(gitignorePath, 'utf8');

  expect(content).toMatch(/dist\//);
  expect(content).toMatch(/\*\.js/);
  expect(content).toMatch(/node_modules\//);
  expect(content).toMatch(/\*\.log/);
});

// 测试 6: package.json 脚本路径
console.log('\n📦 验证 package.json 脚本路径...');

test('package.json 中的脚本应该指向 scripts 目录', () => {
  const packageJsonPath = path.join(process.cwd(), 'package.json');
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));

  const scripts = packageJson.scripts || {};

  if (scripts.cli) {
    expect(scripts.cli).toMatch(/scripts\/cli\.ts/);
  }
  if (scripts.demo) {
    expect(scripts.demo).toMatch(/scripts\/demo\.ts/);
  }
  if (scripts.interactive) {
    expect(scripts.interactive).toMatch(/scripts\/interactive\.ts/);
  }
});

test('package.json 脚本应该输出到 dist/scripts 目录', () => {
  const packageJsonPath = path.join(process.cwd(), 'package.json');
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));

  const scripts = packageJson.scripts || {};

  Object.values(scripts).forEach((script) => {
    if (typeof script === 'string' && script.includes('--outDir')) {
      expect(script).toMatch(/--outDir dist\/scripts/);
    }
  });
});

// 测试 7: 功能验证
console.log('\n⚡ 验证核心功能...');

test('demo 脚本应该能够编译', () => {
  const { execSync } = require('child_process');
  try {
    execSync('npx tsc scripts/demo.ts --noEmit --target ES2020 --module CommonJS --moduleResolution node --esModuleInterop --experimentalDecorators --emitDecoratorMetadata', {
      stdio: 'pipe',
      cwd: process.cwd()
    });
  } catch (error) {
    throw new Error(`TypeScript 编译失败: ${error.message}`);
  }
});

// 输出测试结果
console.log('\n' + '='.repeat(50));
console.log(`🎯 测试完成: ${passedTests}/${totalTests} 通过`);

if (passedTests === totalTests) {
  console.log('🎉 所有测试通过！项目清理成功！');
  process.exit(0);
} else {
  console.log('❌ 部分测试失败，请检查上述错误信息');
  process.exit(1);
}
