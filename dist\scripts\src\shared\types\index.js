"use strict";
/**
 * 共享类型定义
 * 定义了应用中使用的核心接口和类型
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.TYPES = exports.ProcessStatus = exports.ProjectType = exports.CoreEvents = void 0;
var CoreEvents;
(function (CoreEvents) {
    // 项目相关
    CoreEvents["PROJECT_DISCOVERED"] = "project.discovered";
    CoreEvents["PROJECT_UPDATED"] = "project.updated";
    // 进程相关
    CoreEvents["PROCESS_STARTED"] = "process.started";
    CoreEvents["PROCESS_STOPPED"] = "process.stopped";
    CoreEvents["PROCESS_OUTPUT"] = "process.output";
    CoreEvents["PROCESS_ERROR"] = "process.error";
    CoreEvents["PROCESS_CRASHED"] = "process.crashed";
    // 端口相关
    CoreEvents["PORT_AVAILABLE"] = "port.available";
    CoreEvents["PORT_UNAVAILABLE"] = "port.unavailable";
    // 配置相关
    CoreEvents["CONFIG_CHANGED"] = "config.changed";
    // AI相关
    CoreEvents["AI_ANALYSIS_COMPLETE"] = "ai.analysis.complete";
    CoreEvents["AI_SUGGESTION_READY"] = "ai.suggestion.ready";
})(CoreEvents || (exports.CoreEvents = CoreEvents = {}));
// ============= 项目管理 =============
var ProjectType;
(function (ProjectType) {
    ProjectType["NODE_JS"] = "nodejs";
    ProjectType["PYTHON"] = "python";
    ProjectType["JAVA"] = "java";
    ProjectType["DOTNET"] = "dotnet";
    ProjectType["GO"] = "go";
    ProjectType["RUST"] = "rust";
    ProjectType["UNKNOWN"] = "unknown";
})(ProjectType || (exports.ProjectType = ProjectType = {}));
var ProcessStatus;
(function (ProcessStatus) {
    ProcessStatus["STARTING"] = "starting";
    ProcessStatus["RUNNING"] = "running";
    ProcessStatus["STOPPING"] = "stopping";
    ProcessStatus["STOPPED"] = "stopped";
    ProcessStatus["CRASHED"] = "crashed";
})(ProcessStatus || (exports.ProcessStatus = ProcessStatus = {}));
exports.TYPES = {
    // 核心服务
    EventBus: Symbol.for('EventBus'),
    Logger: Symbol.for('Logger'),
    StateRepository: Symbol.for('StateRepository'),
    SecurityVault: Symbol.for('SecurityVault'),
    // 业务服务
    ProjectDiscoveryService: Symbol.for('ProjectDiscoveryService'),
    VersionControlAdapter: Symbol.for('VersionControlAdapter'),
    ProcessLauncher: Symbol.for('ProcessLauncher'),
    PortMonitor: Symbol.for('PortMonitor'),
    // 工作流服务
    WorkflowService: Symbol.for('WorkflowService'),
    ServiceOrchestrator: Symbol.for('ServiceOrchestrator'),
    HealthCheckService: Symbol.for('HealthCheckService'),
    BatchOperationsService: Symbol.for('BatchOperationsService'),
    FileManagerService: Symbol.for('FileManagerService'),
    ConfigService: Symbol.for('ConfigService'),
    // 插件
    GitPlugin: Symbol.for('GitPlugin'),
    NpmPlugin: Symbol.for('NpmPlugin'),
};
