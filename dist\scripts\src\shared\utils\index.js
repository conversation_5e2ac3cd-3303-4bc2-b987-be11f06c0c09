"use strict";
/**
 * 共享工具函数
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateId = generateId;
exports.delay = delay;
exports.debounce = debounce;
exports.throttle = throttle;
exports.fileExists = fileExists;
exports.directoryExists = directoryExists;
exports.detectProjectType = detectProjectType;
exports.formatFileSize = formatFileSize;
exports.formatTime = formatTime;
exports.getRelativeTime = getRelativeTime;
exports.normalizePath = normalizePath;
exports.getProjectNameFromPath = getProjectNameFromPath;
exports.isPortAvailable = isPortAvailable;
exports.safeJsonParse = safeJsonParse;
exports.deepClone = deepClone;
const types_1 = require("../types");
const path = __importStar(require("path"));
const fs = __importStar(require("fs"));
/**
 * 生成唯一ID
 */
function generateId() {
    return Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
}
/**
 * 延迟执行
 */
function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}
/**
 * 防抖函数
 */
function debounce(func, wait) {
    let timeout;
    return (...args) => {
        clearTimeout(timeout);
        timeout = setTimeout(() => func(...args), wait);
    };
}
/**
 * 节流函数
 */
function throttle(func, limit) {
    let inThrottle;
    return (...args) => {
        if (!inThrottle) {
            func(...args);
            inThrottle = true;
            setTimeout(() => (inThrottle = false), limit);
        }
    };
}
/**
 * 检查文件是否存在
 */
async function fileExists(filePath) {
    try {
        await fs.promises.access(filePath);
        return true;
    }
    catch {
        return false;
    }
}
/**
 * 检查目录是否存在
 */
async function directoryExists(dirPath) {
    try {
        const stat = await fs.promises.stat(dirPath);
        return stat.isDirectory();
    }
    catch {
        return false;
    }
}
/**
 * 根据项目文件推断项目类型
 */
async function detectProjectType(projectPath) {
    const files = await fs.promises.readdir(projectPath);
    // Node.js 项目
    if (files.includes('package.json')) {
        return types_1.ProjectType.NODE_JS;
    }
    // Python 项目
    if (files.includes('requirements.txt') || files.includes('setup.py') || files.includes('pyproject.toml')) {
        return types_1.ProjectType.PYTHON;
    }
    // Java 项目
    if (files.includes('pom.xml') || files.includes('build.gradle')) {
        return types_1.ProjectType.JAVA;
    }
    // .NET 项目
    if (files.some(file => file.endsWith('.csproj') || file.endsWith('.sln'))) {
        return types_1.ProjectType.DOTNET;
    }
    // Go 项目
    if (files.includes('go.mod')) {
        return types_1.ProjectType.GO;
    }
    // Rust 项目
    if (files.includes('Cargo.toml')) {
        return types_1.ProjectType.RUST;
    }
    return types_1.ProjectType.UNKNOWN;
}
/**
 * 格式化文件大小
 */
function formatFileSize(bytes) {
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0)
        return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
}
/**
 * 格式化时间
 */
function formatTime(date) {
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
    });
}
/**
 * 获取相对时间
 */
function getRelativeTime(date) {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    if (days > 0)
        return `${days}天前`;
    if (hours > 0)
        return `${hours}小时前`;
    if (minutes > 0)
        return `${minutes}分钟前`;
    return '刚刚';
}
/**
 * 规范化路径
 */
function normalizePath(filePath) {
    return path.normalize(filePath).replace(/\\/g, '/');
}
/**
 * 获取项目名称（从路径中提取）
 */
function getProjectNameFromPath(projectPath) {
    return path.basename(projectPath);
}
/**
 * 检查端口是否可用
 */
function isPortAvailable(port) {
    return new Promise((resolve) => {
        const net = require('net');
        const server = net.createServer();
        server.listen(port, () => {
            server.once('close', () => {
                resolve(true);
            });
            server.close();
        });
        server.on('error', () => {
            resolve(false);
        });
    });
}
/**
 * 安全的JSON解析
 */
function safeJsonParse(json, defaultValue) {
    try {
        return JSON.parse(json);
    }
    catch {
        return defaultValue;
    }
}
/**
 * 深度克隆对象
 */
function deepClone(obj) {
    if (obj === null || typeof obj !== 'object')
        return obj;
    if (obj instanceof Date)
        return new Date(obj.getTime());
    if (obj instanceof Array)
        return obj.map(item => deepClone(item));
    if (typeof obj === 'object') {
        const clonedObj = {};
        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                clonedObj[key] = deepClone(obj[key]);
            }
        }
        return clonedObj;
    }
    return obj;
}
