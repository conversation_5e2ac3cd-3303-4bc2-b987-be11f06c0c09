"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigService = void 0;
const inversify_1 = require("inversify");
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
let ConfigService = class ConfigService {
    constructor() {
        // 使用用户主目录下的配置文件，避免代码更新时丢失
        const os = require('os');
        const userHome = os.homedir();
        const configDir = path.join(userHome, '.devworkbench');
        this.configPath = path.join(configDir, 'app-config.json');
        this.loadConfig();
    }
    loadConfig() {
        try {
            // 确保配置目录存在
            const configDir = path.dirname(this.configPath);
            if (!fs.existsSync(configDir)) {
                fs.mkdirSync(configDir, { recursive: true });
            }
            if (fs.existsSync(this.configPath)) {
                const configData = fs.readFileSync(this.configPath, 'utf-8');
                this.config = JSON.parse(configData);
                // 转换日期字符串为Date对象
                this.config.scanConfigs.forEach(config => {
                    config.createdAt = new Date(config.createdAt);
                    config.updatedAt = new Date(config.updatedAt);
                });
            }
            else {
                // 创建默认配置
                this.config = this.createDefaultConfig();
                this.saveConfig();
            }
        }
        catch (error) {
            console.error('加载配置失败，使用默认配置:', error);
            this.config = this.createDefaultConfig();
        }
    }
    createDefaultConfig() {
        const defaultScanConfig = {
            id: 'default',
            name: '默认扫描配置（当前目录）',
            paths: [process.cwd()], // 使用绝对路径
            recursive: true,
            excludePatterns: [
                'node_modules',
                '.git',
                'dist',
                'build',
                '.next',
                '.nuxt',
                'target',
                'bin',
                'obj',
                '.vscode',
                '.idea'
            ],
            includePatterns: ['*'],
            enabled: true,
            createdAt: new Date(),
            updatedAt: new Date()
        };
        return {
            scanConfigs: [defaultScanConfig],
            defaultScanConfig: 'default',
            autoScanOnStartup: false,
            maxDepth: 5,
            excludeHidden: true
        };
    }
    saveConfig() {
        try {
            const configDir = path.dirname(this.configPath);
            if (!fs.existsSync(configDir)) {
                fs.mkdirSync(configDir, { recursive: true });
            }
            fs.writeFileSync(this.configPath, JSON.stringify(this.config, null, 2));
        }
        catch (error) {
            console.error('保存配置失败:', error);
            throw new Error('配置保存失败');
        }
    }
    // 获取所有配置
    getConfig() {
        return { ...this.config };
    }
    // 获取所有扫描配置
    getScanConfigs() {
        return [...this.config.scanConfigs];
    }
    // 获取特定扫描配置
    getScanConfig(id) {
        return this.config.scanConfigs.find(config => config.id === id);
    }
    // 获取默认扫描配置
    getDefaultScanConfig() {
        return this.getScanConfig(this.config.defaultScanConfig);
    }
    // 添加扫描配置
    addScanConfig(config) {
        const newConfig = {
            ...config,
            id: this.generateId(),
            createdAt: new Date(),
            updatedAt: new Date()
        };
        this.config.scanConfigs.push(newConfig);
        this.saveConfig();
        return newConfig;
    }
    // 更新扫描配置
    updateScanConfig(id, updates) {
        const configIndex = this.config.scanConfigs.findIndex(config => config.id === id);
        if (configIndex === -1) {
            throw new Error('配置不存在');
        }
        this.config.scanConfigs[configIndex] = {
            ...this.config.scanConfigs[configIndex],
            ...updates,
            updatedAt: new Date()
        };
        this.saveConfig();
        return this.config.scanConfigs[configIndex];
    }
    // 删除扫描配置
    deleteScanConfig(id) {
        if (id === this.config.defaultScanConfig) {
            throw new Error('不能删除默认配置');
        }
        const configIndex = this.config.scanConfigs.findIndex(config => config.id === id);
        if (configIndex === -1) {
            return false;
        }
        this.config.scanConfigs.splice(configIndex, 1);
        this.saveConfig();
        return true;
    }
    // 设置默认扫描配置
    setDefaultScanConfig(id) {
        if (!this.getScanConfig(id)) {
            throw new Error('配置不存在');
        }
        this.config.defaultScanConfig = id;
        this.saveConfig();
    }
    // 更新应用配置
    updateAppConfig(updates) {
        this.config = {
            ...this.config,
            ...updates
        };
        this.saveConfig();
        return this.config;
    }
    // 导出配置
    exportConfig() {
        return JSON.stringify(this.config, null, 2);
    }
    // 导入配置
    importConfig(configJson) {
        try {
            const importedConfig = JSON.parse(configJson);
            // 验证配置格式
            if (!importedConfig.scanConfigs || !Array.isArray(importedConfig.scanConfigs)) {
                throw new Error('无效的配置格式');
            }
            // 转换日期字符串
            importedConfig.scanConfigs.forEach((config) => {
                config.createdAt = new Date(config.createdAt);
                config.updatedAt = new Date(config.updatedAt);
            });
            this.config = importedConfig;
            this.saveConfig();
        }
        catch (error) {
            throw new Error('配置导入失败: ' + error.message);
        }
    }
    // 重置为默认配置
    resetToDefault() {
        this.config = this.createDefaultConfig();
        this.saveConfig();
    }
    // 生成唯一ID
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }
    // 验证路径是否存在
    validatePaths(paths) {
        const valid = [];
        const invalid = [];
        paths.forEach(p => {
            try {
                if (fs.existsSync(p)) {
                    valid.push(p);
                }
                else {
                    invalid.push(p);
                }
            }
            catch (error) {
                invalid.push(p);
            }
        });
        return { valid, invalid };
    }
    // 获取路径统计信息
    getPathStats(paths) {
        return paths.map(p => {
            try {
                const absolutePath = path.isAbsolute(p) ? p : path.resolve(p);
                if (fs.existsSync(absolutePath)) {
                    const stats = fs.statSync(absolutePath);
                    return {
                        path: p,
                        absolutePath,
                        exists: true,
                        type: stats.isDirectory() ? 'directory' : 'file',
                        size: stats.isFile() ? stats.size : undefined
                    };
                }
                else {
                    return {
                        path: p,
                        absolutePath,
                        exists: false,
                        type: 'unknown'
                    };
                }
            }
            catch (error) {
                return {
                    path: p,
                    absolutePath: path.isAbsolute(p) ? p : path.resolve(p),
                    exists: false,
                    type: 'error'
                };
            }
        });
    }
    // 规范化路径（转换为绝对路径）
    normalizePaths(paths) {
        return paths.map(p => {
            if (path.isAbsolute(p)) {
                return path.normalize(p);
            }
            else {
                return path.resolve(p);
            }
        });
    }
    // 获取配置文件路径（用于显示给用户）
    getConfigFilePath() {
        return this.configPath;
    }
    // 获取配置目录路径
    getConfigDirectory() {
        return path.dirname(this.configPath);
    }
    // 检查配置文件是否存在
    configFileExists() {
        return fs.existsSync(this.configPath);
    }
    // 备份配置文件
    backupConfig() {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backupPath = this.configPath.replace('.json', `_backup_${timestamp}.json`);
        if (fs.existsSync(this.configPath)) {
            fs.copyFileSync(this.configPath, backupPath);
            return backupPath;
        }
        throw new Error('配置文件不存在，无法备份');
    }
    // 获取系统常用目录
    getCommonDirectories() {
        const os = require('os');
        const userHome = os.homedir();
        const commonDirs = [
            { name: '用户主目录', path: userHome, description: '当前用户的主目录' },
            { name: '桌面', path: path.join(userHome, 'Desktop'), description: '桌面文件夹' },
            { name: '文档', path: path.join(userHome, 'Documents'), description: '文档文件夹' },
            { name: '下载', path: path.join(userHome, 'Downloads'), description: '下载文件夹' },
            { name: '当前工作目录', path: process.cwd(), description: '程序当前运行目录' }
        ];
        // Windows特定目录
        if (process.platform === 'win32') {
            commonDirs.push({ name: 'C盘根目录', path: 'C:\\', description: 'C盘根目录' }, { name: 'Program Files', path: 'C:\\Program Files', description: '程序安装目录' }, { name: 'Program Files (x86)', path: 'C:\\Program Files (x86)', description: '32位程序安装目录' });
        }
        // 过滤存在的目录
        return commonDirs.filter(dir => {
            try {
                return fs.existsSync(dir.path) && fs.statSync(dir.path).isDirectory();
            }
            catch {
                return false;
            }
        });
    }
};
exports.ConfigService = ConfigService;
exports.ConfigService = ConfigService = __decorate([
    (0, inversify_1.injectable)(),
    __metadata("design:paramtypes", [])
], ConfigService);
