"use strict";
/**
 * 健康检查服务
 * 监控服务健康状态并提供自动重启机制
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HealthCheckService = exports.HealthStatus = void 0;
const inversify_1 = require("inversify");
const http = __importStar(require("http"));
const https = __importStar(require("https"));
const url_1 = require("url");
const types_1 = require("../../shared/types");
const utils_1 = require("../../shared/utils");
const event_bus_1 = require("../core/event-bus");
var HealthStatus;
(function (HealthStatus) {
    HealthStatus["HEALTHY"] = "healthy";
    HealthStatus["UNHEALTHY"] = "unhealthy";
    HealthStatus["UNKNOWN"] = "unknown";
    HealthStatus["CHECKING"] = "checking";
})(HealthStatus || (exports.HealthStatus = HealthStatus = {}));
let HealthCheckService = class HealthCheckService {
    constructor(logger, portMonitor, eventBus) {
        this.logger = logger;
        this.portMonitor = portMonitor;
        this.eventBus = eventBus;
        this.monitors = new Map();
        this.DEFAULT_CHECK_INTERVAL = 30000; // 30秒
        this.MAX_CONSECUTIVE_FAILURES = 3;
        this.REQUEST_TIMEOUT = 10000; // 10秒
    }
    /**
     * 开始监控服务健康状态
     */
    async startMonitoring(service) {
        // 如果已经在监控，先停止
        if (this.monitors.has(service.id)) {
            await this.stopMonitoring(service.id);
        }
        const monitor = {
            serviceId: service.id,
            serviceName: service.name,
            config: service,
            status: HealthStatus.UNKNOWN,
            lastCheck: new Date(),
            consecutiveFailures: 0,
            isMonitoring: true,
        };
        this.monitors.set(service.id, monitor);
        // 立即执行一次健康检查
        await this.performHealthCheck(monitor);
        // 设置定期检查
        monitor.interval = setInterval(async () => {
            if (monitor.isMonitoring) {
                await this.performHealthCheck(monitor);
            }
        }, this.DEFAULT_CHECK_INTERVAL);
        this.logger.info(`开始监控服务健康状态: ${service.name}`);
        return monitor;
    }
    /**
     * 停止监控服务
     */
    async stopMonitoring(serviceId) {
        const monitor = this.monitors.get(serviceId);
        if (!monitor) {
            return;
        }
        monitor.isMonitoring = false;
        if (monitor.interval) {
            clearInterval(monitor.interval);
        }
        this.monitors.delete(serviceId);
        this.logger.info(`停止监控服务健康状态: ${monitor.serviceName}`);
    }
    /**
     * 检查服务健康状态
     */
    async checkServiceHealth(service) {
        const startTime = Date.now();
        try {
            // 如果有健康检查URL，使用HTTP检查
            if (service.healthCheckUrl) {
                return await this.performHttpHealthCheck(service.healthCheckUrl, startTime);
            }
            // 如果有端口，检查端口是否可达
            if (service.port) {
                return await this.performPortHealthCheck(service.port, startTime);
            }
            // 默认认为健康（没有具体的检查方式）
            return {
                healthy: true,
                responseTime: Date.now() - startTime,
                timestamp: new Date(),
            };
        }
        catch (error) {
            return {
                healthy: false,
                responseTime: Date.now() - startTime,
                error: error.message,
                timestamp: new Date(),
            };
        }
    }
    /**
     * 获取所有监控的服务
     */
    getMonitoredServices() {
        return Array.from(this.monitors.values());
    }
    /**
     * 停止所有监控
     */
    async stopAllMonitoring() {
        const serviceIds = Array.from(this.monitors.keys());
        const stopPromises = serviceIds.map(id => this.stopMonitoring(id));
        await Promise.allSettled(stopPromises);
        this.logger.info('已停止所有服务健康监控');
    }
    /**
     * 执行健康检查
     */
    async performHealthCheck(monitor) {
        monitor.status = HealthStatus.CHECKING;
        monitor.lastCheck = new Date();
        try {
            const result = await this.checkServiceHealth(monitor.config);
            if (result.healthy) {
                // 健康状态
                const wasUnhealthy = monitor.status === HealthStatus.UNHEALTHY ||
                    monitor.status === HealthStatus.UNKNOWN;
                monitor.status = HealthStatus.HEALTHY;
                monitor.consecutiveFailures = 0;
                if (wasUnhealthy) {
                    this.logger.info(`服务恢复健康: ${monitor.serviceName}`);
                    this.eventBus.emitEvent(types_1.CoreEvents.PROCESS_STARTED, {
                        type: 'service_health_recovered',
                        serviceId: monitor.serviceId,
                        serviceName: monitor.serviceName,
                    });
                }
            }
            else {
                // 不健康状态
                monitor.status = HealthStatus.UNHEALTHY;
                monitor.consecutiveFailures++;
                this.logger.warn(`服务健康检查失败: ${monitor.serviceName} (连续失败 ${monitor.consecutiveFailures} 次)`, {
                    error: result.error,
                    responseTime: result.responseTime,
                });
                // 发射健康检查失败事件
                this.eventBus.emitEvent(types_1.CoreEvents.PROCESS_ERROR, {
                    type: 'service_health_check_failed',
                    serviceId: monitor.serviceId,
                    serviceName: monitor.serviceName,
                    consecutiveFailures: monitor.consecutiveFailures,
                    error: result.error,
                });
                // 如果连续失败次数达到阈值，触发重启
                if (monitor.consecutiveFailures >= this.MAX_CONSECUTIVE_FAILURES) {
                    await this.handleServiceFailure(monitor);
                }
            }
        }
        catch (error) {
            monitor.status = HealthStatus.UNHEALTHY;
            monitor.consecutiveFailures++;
            this.logger.error(`健康检查异常: ${monitor.serviceName}`, error);
        }
    }
    /**
     * 执行HTTP健康检查
     */
    async performHttpHealthCheck(url, startTime) {
        return new Promise((resolve) => {
            const parsedUrl = new url_1.URL(url);
            const isHttps = parsedUrl.protocol === 'https:';
            const client = isHttps ? https : http;
            const options = {
                hostname: parsedUrl.hostname,
                port: parsedUrl.port || (isHttps ? 443 : 80),
                path: parsedUrl.pathname + parsedUrl.search,
                method: 'GET',
                timeout: this.REQUEST_TIMEOUT,
                headers: {
                    'User-Agent': 'DevWorkbench-HealthCheck/1.0',
                },
            };
            const req = client.request(options, (res) => {
                const responseTime = Date.now() - startTime;
                const healthy = res.statusCode !== undefined && res.statusCode >= 200 && res.statusCode < 400;
                resolve({
                    healthy,
                    responseTime,
                    statusCode: res.statusCode,
                    timestamp: new Date(),
                });
                // 消费响应数据以避免内存泄漏
                res.resume();
            });
            req.on('error', (error) => {
                resolve({
                    healthy: false,
                    responseTime: Date.now() - startTime,
                    error: error.message,
                    timestamp: new Date(),
                });
            });
            req.on('timeout', () => {
                req.destroy();
                resolve({
                    healthy: false,
                    responseTime: Date.now() - startTime,
                    error: 'Request timeout',
                    timestamp: new Date(),
                });
            });
            req.end();
        });
    }
    /**
     * 执行端口健康检查
     */
    async performPortHealthCheck(port, startTime) {
        try {
            // 检查端口是否被占用（服务是否在运行）
            const portInUse = !(await this.portMonitor.checkPort(port));
            return {
                healthy: portInUse,
                responseTime: Date.now() - startTime,
                timestamp: new Date(),
            };
        }
        catch (error) {
            return {
                healthy: false,
                responseTime: Date.now() - startTime,
                error: error.message,
                timestamp: new Date(),
            };
        }
    }
    /**
     * 处理服务失败
     */
    async handleServiceFailure(monitor) {
        this.logger.error(`服务连续失败达到阈值，触发故障处理: ${monitor.serviceName}`);
        // 发射服务故障事件
        this.eventBus.emitEvent(types_1.CoreEvents.PROCESS_CRASHED, {
            type: 'service_health_failure',
            serviceId: monitor.serviceId,
            serviceName: monitor.serviceName,
            consecutiveFailures: monitor.consecutiveFailures,
            autoRestart: monitor.config.autoRestart,
        });
        // 如果启用了自动重启
        if (monitor.config.autoRestart) {
            await this.attemptServiceRestart(monitor);
        }
    }
    /**
     * 尝试重启服务
     */
    async attemptServiceRestart(monitor) {
        this.logger.info(`尝试自动重启服务: ${monitor.serviceName}`);
        try {
            // 等待重启延迟
            const restartDelay = monitor.config.restartDelay || 5000;
            await (0, utils_1.delay)(restartDelay);
            // 发射重启请求事件
            this.eventBus.emitEvent(types_1.CoreEvents.PROCESS_STARTED, {
                type: 'service_restart_requested',
                serviceId: monitor.serviceId,
                serviceName: monitor.serviceName,
                reason: 'health_check_failure',
            });
            // 重置失败计数
            monitor.consecutiveFailures = 0;
            monitor.status = HealthStatus.UNKNOWN;
            this.logger.info(`服务重启请求已发送: ${monitor.serviceName}`);
        }
        catch (error) {
            this.logger.error(`服务重启失败: ${monitor.serviceName}`, error);
        }
    }
    /**
     * 获取服务健康统计
     */
    getHealthStatistics() {
        const monitors = this.getMonitoredServices();
        return {
            total: monitors.length,
            healthy: monitors.filter(m => m.status === HealthStatus.HEALTHY).length,
            unhealthy: monitors.filter(m => m.status === HealthStatus.UNHEALTHY).length,
            unknown: monitors.filter(m => m.status === HealthStatus.UNKNOWN).length,
            checking: monitors.filter(m => m.status === HealthStatus.CHECKING).length,
        };
    }
};
exports.HealthCheckService = HealthCheckService;
exports.HealthCheckService = HealthCheckService = __decorate([
    (0, inversify_1.injectable)(),
    __param(0, (0, inversify_1.inject)(types_1.TYPES.Logger)),
    __param(1, (0, inversify_1.inject)(types_1.TYPES.PortMonitor)),
    __param(2, (0, inversify_1.inject)(types_1.TYPES.EventBus)),
    __metadata("design:paramtypes", [Object, Object, event_bus_1.EventBus])
], HealthCheckService);
