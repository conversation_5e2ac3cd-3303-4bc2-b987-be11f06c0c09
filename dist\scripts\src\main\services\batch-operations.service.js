"use strict";
/**
 * 批量操作服务
 * 处理多项目的批量操作和统一管理
 */
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BatchOperationsService = void 0;
const inversify_1 = require("inversify");
const types_1 = require("../../shared/types");
const event_bus_1 = require("../core/event-bus");
let BatchOperationsService = class BatchOperationsService {
    constructor(logger, gitAdapter, projectService, eventBus) {
        this.logger = logger;
        this.gitAdapter = gitAdapter;
        this.projectService = projectService;
        this.eventBus = eventBus;
    }
    /**
     * 批量执行Git Pull
     */
    async batchGitPull(projectIds) {
        return this.executeBatchOperation('git_pull', projectIds, async (project) => {
            await this.gitAdapter.pull(project.path);
            return { message: 'Git pull 成功' };
        });
    }
    /**
     * 批量获取Git状态
     */
    async batchGitStatus(projectIds) {
        return this.executeBatchOperation('git_status', projectIds, async (project) => {
            const status = await this.gitAdapter.getStatus(project.path);
            return status;
        });
    }
    /**
     * 批量刷新项目
     */
    async batchRefreshProjects(projectIds) {
        return this.executeBatchOperation('refresh_projects', projectIds, async (project) => {
            await this.projectService.refreshProject(project.id);
            return { message: '项目刷新成功' };
        });
    }
    /**
     * 批量在VS Code中打开
     */
    async batchOpenInVSCode(projectIds) {
        return this.executeBatchOperation('open_vscode', projectIds, async (project) => {
            const { spawn } = require('child_process');
            spawn('code', [project.path], { detached: true });
            return { message: '已在VS Code中打开' };
        });
    }
    /**
     * 批量在终端中打开
     */
    async batchOpenInTerminal(projectIds) {
        return this.executeBatchOperation('open_terminal', projectIds, async (project) => {
            const { spawn } = require('child_process');
            if (process.platform === 'win32') {
                spawn('powershell', ['-Command', `cd "${project.path}"; powershell`], { detached: true });
            }
            else {
                spawn('open', ['-a', 'Terminal', project.path], { detached: true });
            }
            return { message: '已在终端中打开' };
        });
    }
    /**
     * 批量在文件管理器中打开
     */
    async batchOpenInExplorer(projectIds) {
        return this.executeBatchOperation('open_explorer', projectIds, async (project) => {
            const { shell } = require('electron');
            await shell.openPath(project.path);
            return { message: '已在文件管理器中打开' };
        });
    }
    /**
     * 执行自定义批量命令
     */
    async executeCustomBatchCommand(projectIds, command) {
        return this.executeBatchOperation('custom_command', projectIds, async (project) => {
            const { spawn } = require('child_process');
            return new Promise((resolve, reject) => {
                const child = spawn(command, [], {
                    cwd: project.path,
                    shell: true,
                    stdio: 'pipe',
                });
                let stdout = '';
                let stderr = '';
                child.stdout?.on('data', (data) => {
                    stdout += data.toString();
                });
                child.stderr?.on('data', (data) => {
                    stderr += data.toString();
                });
                child.on('close', (code) => {
                    if (code === 0) {
                        resolve({
                            message: '命令执行成功',
                            stdout: stdout.trim(),
                            stderr: stderr.trim(),
                        });
                    }
                    else {
                        reject(new Error(`命令执行失败 (退出码: ${code})\n${stderr}`));
                    }
                });
                child.on('error', (error) => {
                    reject(error);
                });
                // 设置超时
                setTimeout(() => {
                    child.kill();
                    reject(new Error('命令执行超时'));
                }, 30000); // 30秒超时
            });
        });
    }
    /**
     * 执行批量操作的通用方法
     */
    async executeBatchOperation(operationType, projectIds, operation) {
        const operationId = `batch_${operationType}_${Date.now()}`;
        const startedAt = new Date();
        this.logger.info(`开始批量操作: ${operationType}, 项目数量: ${projectIds.length}`);
        // 获取项目信息
        const projects = this.projectService.getProjects().filter(p => projectIds.includes(p.id));
        const results = [];
        // 发射批量操作开始事件
        this.eventBus.emitEvent(types_1.CoreEvents.PROCESS_STARTED, {
            type: 'batch_operation_started',
            operationId,
            operationType,
            projectCount: projects.length,
        });
        // 并发执行操作（限制并发数）
        const concurrencyLimit = 5;
        const chunks = this.chunkArray(projects, concurrencyLimit);
        for (const chunk of chunks) {
            const chunkPromises = chunk.map(async (project) => {
                const operationStartTime = Date.now();
                try {
                    const data = await operation(project);
                    const duration = Date.now() - operationStartTime;
                    const result = {
                        projectId: project.id,
                        projectName: project.name,
                        success: true,
                        data,
                        duration,
                    };
                    results.push(result);
                    // 发射单个操作成功事件
                    this.eventBus.emitEvent(types_1.CoreEvents.PROCESS_OUTPUT, {
                        type: 'batch_operation_item_success',
                        operationId,
                        projectId: project.id,
                        projectName: project.name,
                        data,
                    });
                }
                catch (error) {
                    const duration = Date.now() - operationStartTime;
                    const result = {
                        projectId: project.id,
                        projectName: project.name,
                        success: false,
                        error: error.message,
                        duration,
                    };
                    results.push(result);
                    // 发射单个操作失败事件
                    this.eventBus.emitEvent(types_1.CoreEvents.PROCESS_ERROR, {
                        type: 'batch_operation_item_error',
                        operationId,
                        projectId: project.id,
                        projectName: project.name,
                        error: error.message,
                    });
                    this.logger.error(`批量操作失败: ${project.name}`, error);
                }
            });
            // 等待当前批次完成
            await Promise.allSettled(chunkPromises);
        }
        const completedAt = new Date();
        const duration = completedAt.getTime() - startedAt.getTime();
        const successCount = results.filter(r => r.success).length;
        const failureCount = results.filter(r => !r.success).length;
        const batchResult = {
            operationId,
            operationType,
            totalCount: projects.length,
            successCount,
            failureCount,
            results,
            startedAt,
            completedAt,
            duration,
        };
        // 发射批量操作完成事件
        this.eventBus.emitEvent(types_1.CoreEvents.PROCESS_STOPPED, {
            type: 'batch_operation_completed',
            operationId,
            operationType,
            result: batchResult,
        });
        this.logger.info(`批量操作完成: ${operationType}, 成功: ${successCount}, 失败: ${failureCount}, 耗时: ${duration}ms`);
        return batchResult;
    }
    /**
     * 将数组分块
     */
    chunkArray(array, chunkSize) {
        const chunks = [];
        for (let i = 0; i < array.length; i += chunkSize) {
            chunks.push(array.slice(i, i + chunkSize));
        }
        return chunks;
    }
    /**
     * 获取批量操作统计信息
     */
    getBatchOperationStats() {
        // 这里可以实现统计逻辑
        // 为简化，返回模拟数据
        return {
            totalOperations: 0,
            successfulOperations: 0,
            failedOperations: 0,
            averageDuration: 0,
        };
    }
};
exports.BatchOperationsService = BatchOperationsService;
exports.BatchOperationsService = BatchOperationsService = __decorate([
    (0, inversify_1.injectable)(),
    __param(0, (0, inversify_1.inject)(types_1.TYPES.Logger)),
    __param(1, (0, inversify_1.inject)(types_1.TYPES.VersionControlAdapter)),
    __param(2, (0, inversify_1.inject)(types_1.TYPES.ProjectDiscoveryService)),
    __param(3, (0, inversify_1.inject)(types_1.TYPES.EventBus)),
    __metadata("design:paramtypes", [Object, Object, Object, event_bus_1.EventBus])
], BatchOperationsService);
