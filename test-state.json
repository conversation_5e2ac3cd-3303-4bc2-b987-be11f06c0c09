{"executableTasks": [{"id": "task-npm-start", "name": "启动开发服务器", "command": "npm start", "description": "启动项目的开发服务器", "category": "development", "icon": "🚀", "createdAt": "2025-07-30T12:24:25.707Z", "updatedAt": "2025-07-30T12:24:25.707Z"}, {"id": "task-npm-build", "name": "构建项目", "command": "npm run build", "description": "构建生产版本", "category": "build", "icon": "🔨", "createdAt": "2025-07-30T12:24:25.707Z", "updatedAt": "2025-07-30T12:24:25.707Z"}, {"id": "task-npm-test", "name": "运行测试", "command": "npm test", "description": "运行项目测试", "category": "test", "icon": "🧪", "createdAt": "2025-07-30T12:24:25.707Z", "updatedAt": "2025-07-30T12:24:25.707Z"}, {"id": "task-git-status", "name": "Git状态", "command": "git status", "description": "查看Git仓库状态", "category": "git", "icon": "📊", "createdAt": "2025-07-30T12:24:25.707Z", "updatedAt": "2025-07-30T12:24:25.707Z"}, {"id": "task-n8n-start", "name": "启动N8N", "command": "n8n start", "description": "启动N8N工作流引擎", "category": "automation", "icon": "🔄", "createdAt": "2025-07-30T12:24:25.707Z", "updatedAt": "2025-07-30T12:24:25.707Z"}]}