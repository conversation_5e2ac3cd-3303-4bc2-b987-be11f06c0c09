"use strict";
/**
 * 项目发现服务实现
 * 扫描本地目录，发现和管理Git项目
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProjectDiscoveryService = void 0;
const inversify_1 = require("inversify");
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const types_1 = require("../../shared/types");
const utils_1 = require("../../shared/utils");
const event_bus_1 = require("../core/event-bus");
let ProjectDiscoveryService = class ProjectDiscoveryService {
    constructor(logger, stateRepository, gitAdapter, eventBus) {
        this.logger = logger;
        this.stateRepository = stateRepository;
        this.gitAdapter = gitAdapter;
        this.eventBus = eventBus;
        this.projects = new Map();
        this.loadProjectsFromStorage();
    }
    /**
     * 扫描指定目录下的Git仓库
     */
    async scanDirectory(scanPath) {
        this.logger.info(`开始扫描目录: ${scanPath}`);
        if (!(await (0, utils_1.directoryExists)(scanPath))) {
            this.logger.warn(`目录不存在: ${scanPath}`);
            return [];
        }
        const discoveredProjects = [];
        try {
            await this.scanDirectoryRecursive(scanPath, discoveredProjects, 0, 3);
            // 更新项目列表
            for (const project of discoveredProjects) {
                this.projects.set(project.id, project);
                this.eventBus.emitEvent(types_1.CoreEvents.PROJECT_DISCOVERED, project);
            }
            // 保存到存储
            await this.saveProjectsToStorage();
            this.logger.info(`扫描完成，发现 ${discoveredProjects.length} 个项目`);
            return discoveredProjects;
        }
        catch (error) {
            this.logger.error(`扫描目录失败: ${scanPath}`, error);
            return [];
        }
    }
    /**
     * 递归扫描目录
     */
    async scanDirectoryRecursive(dirPath, projects, depth, maxDepth) {
        if (depth > maxDepth)
            return;
        try {
            const entries = await fs.promises.readdir(dirPath, { withFileTypes: true });
            // 检查当前目录是否是Git仓库
            if (await this.gitAdapter.isRepository(dirPath)) {
                const project = await this.createProjectFromPath(dirPath);
                if (project) {
                    projects.push(project);
                    return; // 找到Git仓库后不再深入扫描
                }
            }
            // 继续扫描子目录
            for (const entry of entries) {
                if (entry.isDirectory() && !this.shouldSkipDirectory(entry.name)) {
                    const subDirPath = path.join(dirPath, entry.name);
                    await this.scanDirectoryRecursive(subDirPath, projects, depth + 1, maxDepth);
                }
            }
        }
        catch (error) {
            this.logger.debug(`无法读取目录: ${dirPath}`, error);
        }
    }
    /**
     * 从路径创建项目对象
     */
    async createProjectFromPath(projectPath) {
        try {
            const projectName = (0, utils_1.getProjectNameFromPath)(projectPath);
            const projectType = await (0, utils_1.detectProjectType)(projectPath);
            const gitInfo = await this.gitAdapter.getStatus(projectPath);
            const project = {
                id: (0, utils_1.generateId)(),
                name: projectName,
                path: projectPath,
                type: projectType,
                gitInfo,
                lastAccessed: new Date(),
            };
            // 新增：自动发现项目脚本
            try {
                const entries = await fs.promises.readdir(projectPath);
                const scripts = [];
                for (const entry of entries) {
                    if (entry.endsWith('.bat')) {
                        scripts.push({ name: entry, path: path.join(projectPath, entry), type: 'bat' });
                    }
                    else if (entry.endsWith('.ps1')) {
                        scripts.push({ name: entry, path: path.join(projectPath, entry), type: 'ps1' });
                    }
                }
                if (scripts.length > 0) {
                    project.detectedScripts = scripts;
                }
            }
            catch (scriptError) {
                this.logger.warn(`扫描项目脚本失败: ${projectPath}`, scriptError);
            }
            return project;
        }
        catch (error) {
            this.logger.error(`创建项目对象失败: ${projectPath}`, error);
            return null;
        }
    }
    /**
     * 判断是否应该跳过目录
     */
    shouldSkipDirectory(dirName) {
        const skipDirs = [
            'node_modules',
            '.git',
            '.vscode',
            '.idea',
            'dist',
            'build',
            'target',
            '__pycache__',
            '.pytest_cache',
            'venv',
            'env',
            '.env',
        ];
        return skipDirs.includes(dirName) || dirName.startsWith('.');
    }
    /**
     * 获取所有项目
     */
    getProjects() {
        return Array.from(this.projects.values());
    }
    /**
     * 刷新指定项目
     */
    async refreshProject(projectId) {
        const project = this.projects.get(projectId);
        if (!project) {
            this.logger.warn(`项目不存在: ${projectId}`);
            return;
        }
        try {
            // 检查项目路径是否仍然存在
            if (!(await (0, utils_1.directoryExists)(project.path))) {
                this.logger.warn(`项目路径不存在，移除项目: ${project.path}`);
                await this.removeProject(projectId);
                return;
            }
            // 更新Git信息
            if (await this.gitAdapter.isRepository(project.path)) {
                project.gitInfo = await this.gitAdapter.getStatus(project.path);
            }
            // 更新项目类型
            project.type = await (0, utils_1.detectProjectType)(project.path);
            project.lastAccessed = new Date();
            this.projects.set(projectId, project);
            await this.saveProjectsToStorage();
            this.eventBus.emitEvent(types_1.CoreEvents.PROJECT_UPDATED, project);
            this.logger.info(`项目已刷新: ${project.name}`);
        }
        catch (error) {
            this.logger.error(`刷新项目失败: ${project.name}`, error);
        }
    }
    /**
     * 添加项目
     */
    async addProject(project) {
        this.projects.set(project.id, project);
        await this.saveProjectsToStorage();
        this.eventBus.emitEvent(types_1.CoreEvents.PROJECT_DISCOVERED, project);
        this.logger.info(`项目已添加: ${project.name}`);
    }
    /**
     * 移除项目
     */
    async removeProject(projectId) {
        const project = this.projects.get(projectId);
        if (project) {
            this.projects.delete(projectId);
            await this.saveProjectsToStorage();
            this.logger.info(`项目已移除: ${project.name}`);
        }
    }
    /**
     * 从存储加载项目
     */
    async loadProjectsFromStorage() {
        try {
            const storedProjects = await this.stateRepository.get('projects') || [];
            for (const project of storedProjects) {
                this.projects.set(project.id, project);
            }
            this.logger.info(`从存储加载了 ${storedProjects.length} 个项目`);
        }
        catch (error) {
            this.logger.error('加载项目失败', error);
        }
    }
    /**
     * 保存项目到存储
     */
    async saveProjectsToStorage() {
        try {
            const projectsArray = Array.from(this.projects.values());
            await this.stateRepository.set('projects', projectsArray);
        }
        catch (error) {
            this.logger.error('保存项目失败', error);
        }
    }
};
exports.ProjectDiscoveryService = ProjectDiscoveryService;
exports.ProjectDiscoveryService = ProjectDiscoveryService = __decorate([
    (0, inversify_1.injectable)(),
    __param(0, (0, inversify_1.inject)(types_1.TYPES.Logger)),
    __param(1, (0, inversify_1.inject)(types_1.TYPES.StateRepository)),
    __param(2, (0, inversify_1.inject)(types_1.TYPES.VersionControlAdapter)),
    __param(3, (0, inversify_1.inject)(types_1.TYPES.EventBus)),
    __metadata("design:paramtypes", [Object, Object, Object, event_bus_1.EventBus])
], ProjectDiscoveryService);
