"use strict";
/**
 * 工作流管理服务
 * 管理启动组、服务配置和工作流执行
 */
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowService = void 0;
const inversify_1 = require("inversify");
const types_1 = require("../../shared/types");
const utils_1 = require("../../shared/utils");
const event_bus_1 = require("../core/event-bus");
let WorkflowService = class WorkflowService {
    constructor(logger, stateRepository, eventBus) {
        this.logger = logger;
        this.stateRepository = stateRepository;
        this.eventBus = eventBus;
        this.workflowGroups = new Map();
        this.loadWorkflowGroups();
    }
    /**
     * 创建工作流组
     */
    async createWorkflowGroup(group) {
        const now = new Date();
        const workflowGroup = {
            id: (0, utils_1.generateId)(),
            ...group,
            services: group.services.map((service, index) => ({
                ...service,
                id: service.id || (0, utils_1.generateId)(),
                orderIndex: service.orderIndex !== undefined ? service.orderIndex : index,
                source: service.source || 'command',
            })),
            createdAt: now,
            updatedAt: now,
        };
        this.workflowGroups.set(workflowGroup.id, workflowGroup);
        await this.saveWorkflowGroups();
        this.eventBus.emitEvent(types_1.CoreEvents.CONFIG_CHANGED, {
            type: 'workflow_group_created',
            groupId: workflowGroup.id,
            group: workflowGroup,
        });
        this.logger.info(`工作流组已创建: ${workflowGroup.name}`);
        return workflowGroup;
    }
    /**
     * 更新工作流组
     */
    async updateWorkflowGroup(groupId, updates) {
        const group = this.workflowGroups.get(groupId);
        if (!group) {
            throw new Error(`工作流组不存在: ${groupId}`);
        }
        const updatedGroup = {
            ...group,
            ...updates,
            id: groupId, // 确保ID不被覆盖
            updatedAt: new Date(),
        };
        this.workflowGroups.set(groupId, updatedGroup);
        await this.saveWorkflowGroups();
        this.eventBus.emitEvent(types_1.CoreEvents.CONFIG_CHANGED, {
            type: 'workflow_group_updated',
            groupId,
            group: updatedGroup,
        });
        this.logger.info(`工作流组已更新: ${updatedGroup.name}`);
    }
    /**
     * 删除工作流组
     */
    async deleteWorkflowGroup(groupId) {
        const group = this.workflowGroups.get(groupId);
        if (!group) {
            throw new Error(`工作流组不存在: ${groupId}`);
        }
        this.workflowGroups.delete(groupId);
        await this.saveWorkflowGroups();
        this.eventBus.emitEvent(types_1.CoreEvents.CONFIG_CHANGED, {
            type: 'workflow_group_deleted',
            groupId,
            groupName: group.name,
        });
        this.logger.info(`工作流组已删除: ${group.name}`);
    }
    /**
     * 获取所有工作流组
     */
    async getWorkflowGroups() {
        return Array.from(this.workflowGroups.values());
    }
    /**
     * 获取指定工作流组
     */
    async getWorkflowGroup(groupId) {
        return this.workflowGroups.get(groupId) || null;
    }
    /**
     * 向工作流组添加服务
     */
    async addServiceToGroup(groupId, service) {
        const group = this.workflowGroups.get(groupId);
        if (!group) {
            throw new Error(`工作流组不存在: ${groupId}`);
        }
        const serviceConfig = {
            id: (0, utils_1.generateId)(),
            ...service,
        };
        group.services.push(serviceConfig);
        group.updatedAt = new Date();
        this.workflowGroups.set(groupId, group);
        await this.saveWorkflowGroups();
        this.eventBus.emitEvent(types_1.CoreEvents.CONFIG_CHANGED, {
            type: 'service_added_to_group',
            groupId,
            serviceId: serviceConfig.id,
            service: serviceConfig,
        });
        this.logger.info(`服务已添加到工作流组: ${service.name} -> ${group.name}`);
    }
    /**
     * 从工作流组移除服务
     */
    async removeServiceFromGroup(groupId, serviceId) {
        const group = this.workflowGroups.get(groupId);
        if (!group) {
            throw new Error(`工作流组不存在: ${groupId}`);
        }
        const serviceIndex = group.services.findIndex(s => s.id === serviceId);
        if (serviceIndex === -1) {
            throw new Error(`服务不存在: ${serviceId}`);
        }
        const removedService = group.services.splice(serviceIndex, 1)[0];
        group.updatedAt = new Date();
        this.workflowGroups.set(groupId, group);
        await this.saveWorkflowGroups();
        this.eventBus.emitEvent(types_1.CoreEvents.CONFIG_CHANGED, {
            type: 'service_removed_from_group',
            groupId,
            serviceId,
            serviceName: removedService.name,
        });
        this.logger.info(`服务已从工作流组移除: ${removedService.name} <- ${group.name}`);
    }
    /**
     * 更新服务配置
     */
    async updateServiceConfig(groupId, serviceId, updates) {
        const group = this.workflowGroups.get(groupId);
        if (!group) {
            throw new Error(`工作流组不存在: ${groupId}`);
        }
        const serviceIndex = group.services.findIndex(s => s.id === serviceId);
        if (serviceIndex === -1) {
            throw new Error(`服务不存在: ${serviceId}`);
        }
        group.services[serviceIndex] = {
            ...group.services[serviceIndex],
            ...updates,
            id: serviceId, // 确保ID不被覆盖
        };
        group.updatedAt = new Date();
        this.workflowGroups.set(groupId, group);
        await this.saveWorkflowGroups();
        this.eventBus.emitEvent(types_1.CoreEvents.CONFIG_CHANGED, {
            type: 'service_config_updated',
            groupId,
            serviceId,
            service: group.services[serviceIndex],
        });
        this.logger.info(`服务配置已更新: ${group.services[serviceIndex].name}`);
    }
    /**
     * 验证工作流组配置
     */
    async validateWorkflowGroup(group) {
        const errors = [];
        // 检查基本信息
        if (!group.name || group.name.trim().length === 0) {
            errors.push('工作流组名称不能为空');
        }
        if (group.services.length === 0) {
            errors.push('工作流组至少需要包含一个服务');
        }
        // 检查服务配置
        const serviceNames = new Set();
        const servicePorts = new Set();
        for (const service of group.services) {
            // 检查服务名称唯一性
            if (serviceNames.has(service.name)) {
                errors.push(`服务名称重复: ${service.name}`);
            }
            serviceNames.add(service.name);
            // 检查端口冲突
            if (service.port && servicePorts.has(service.port)) {
                errors.push(`端口冲突: ${service.port}`);
            }
            if (service.port) {
                servicePorts.add(service.port);
            }
            // 检查命令
            if (!service.command || service.command.trim().length === 0) {
                errors.push(`服务命令不能为空: ${service.name}`);
            }
            // 检查依赖循环
            if (service.dependsOn && service.dependsOn.includes(service.id)) {
                errors.push(`服务不能依赖自己: ${service.name}`);
            }
        }
        // 检查依赖关系
        const serviceIds = new Set(group.services.map(s => s.id));
        for (const service of group.services) {
            if (service.dependsOn) {
                for (const depId of service.dependsOn) {
                    if (!serviceIds.has(depId)) {
                        errors.push(`服务依赖不存在: ${service.name} -> ${depId}`);
                    }
                }
            }
        }
        return errors;
    }
    /**
     * 导出工作流组
     */
    async exportWorkflowGroup(groupId) {
        const group = this.workflowGroups.get(groupId);
        if (!group) {
            throw new Error(`工作流组不存在: ${groupId}`);
        }
        const exportData = {
            version: '1.0',
            exportedAt: new Date().toISOString(),
            group: {
                ...group,
                id: undefined, // 导出时移除ID，导入时重新生成
            },
        };
        return JSON.stringify(exportData, null, 2);
    }
    /**
     * 导入工作流组
     */
    async importWorkflowGroup(data) {
        try {
            const importData = JSON.parse(data);
            if (!importData.group) {
                throw new Error('无效的导入数据格式');
            }
            const group = importData.group;
            // 验证导入的数据
            const errors = await this.validateWorkflowGroup(group);
            if (errors.length > 0) {
                throw new Error(`导入数据验证失败: ${errors.join(', ')}`);
            }
            // 创建新的工作流组
            return await this.createWorkflowGroup(group);
        }
        catch (error) {
            this.logger.error('导入工作流组失败', error);
            throw error;
        }
    }
    /**
     * 从存储加载工作流组
     */
    async loadWorkflowGroups() {
        try {
            const storedGroups = await this.stateRepository.get('workflowGroups') || [];
            for (const group of storedGroups) {
                this.workflowGroups.set(group.id, group);
            }
            this.logger.info(`从存储加载了 ${storedGroups.length} 个工作流组`);
        }
        catch (error) {
            this.logger.error('加载工作流组失败', error);
        }
    }
    /**
     * 重新排序工作流组中的服务
     */
    async reorderServices(groupId, orderedServiceIds) {
        const group = this.workflowGroups.get(groupId);
        if (!group) {
            throw new Error(`工作流组不存在: ${groupId}`);
        }
        // 创建一个服务ID到索引的映射
        const orderMap = new Map();
        orderedServiceIds.forEach((id, index) => {
            orderMap.set(id, index);
        });
        // 更新每个服务的 orderIndex
        group.services.forEach(service => {
            if (orderMap.has(service.id)) {
                service.orderIndex = orderMap.get(service.id);
            }
        });
        // 按新的 orderIndex 排序内部数组以保持一致性
        group.services.sort((a, b) => a.orderIndex - b.orderIndex);
        group.updatedAt = new Date();
        this.workflowGroups.set(groupId, group);
        await this.saveWorkflowGroups();
        this.eventBus.emitEvent(types_1.CoreEvents.CONFIG_CHANGED, {
            type: 'workflow_group_reordered',
            groupId,
        });
        this.logger.info(`工作流组服务已重新排序: ${group.name}`);
    }
    /**
     * 保存工作流组到存储
     */
    async saveWorkflowGroups() {
        try {
            const groupsArray = Array.from(this.workflowGroups.values());
            await this.stateRepository.set('workflowGroups', groupsArray);
        }
        catch (error) {
            this.logger.error('保存工作流组失败', error);
        }
    }
};
exports.WorkflowService = WorkflowService;
exports.WorkflowService = WorkflowService = __decorate([
    (0, inversify_1.injectable)(),
    __param(0, (0, inversify_1.inject)(types_1.TYPES.Logger)),
    __param(1, (0, inversify_1.inject)(types_1.TYPES.StateRepository)),
    __param(2, (0, inversify_1.inject)(types_1.TYPES.EventBus)),
    __metadata("design:paramtypes", [Object, Object, event_bus_1.EventBus])
], WorkflowService);
