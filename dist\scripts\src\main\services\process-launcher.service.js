"use strict";
/**
 * 进程启动器服务实现
 * 管理和控制子进程的启动、停止和监控
 */
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProcessLauncher = void 0;
const inversify_1 = require("inversify");
const child_process_1 = require("child_process");
const types_1 = require("../../shared/types");
const utils_1 = require("../../shared/utils");
const event_bus_1 = require("../core/event-bus");
let ProcessLauncher = class ProcessLauncher {
    constructor(logger, eventBus) {
        this.logger = logger;
        this.eventBus = eventBus;
        this.processes = new Map();
    }
    /**
     * 启动进程
     */
    async start(config) {
        this.logger.info(`启动进程: ${config.name} (${config.command})`);
        try {
            // 检查是否已经有同名进程在运行
            const existingProcess = Array.from(this.processes.values())
                .find(p => p.config.name === config.name && p.status === types_1.ProcessStatus.RUNNING);
            if (existingProcess) {
                this.logger.warn(`进程已在运行: ${config.name}`);
                return existingProcess;
            }
            const handle = await this.createProcessHandle(config);
            this.processes.set(handle.id, handle);
            // 发射进程启动事件
            this.eventBus.emitEvent(types_1.CoreEvents.PROCESS_STARTED, {
                processId: handle.id,
                config: config,
            });
            return handle;
        }
        catch (error) {
            this.logger.error(`启动进程失败: ${config.name}`, error);
            throw error;
        }
    }
    /**
     * 创建进程句柄
     */
    async createProcessHandle(config) {
        const processId = (0, utils_1.generateId)();
        // 使用标准 spawn 创建子进程
        const childProcess = (0, child_process_1.spawn)(config.command, config.args || [], {
            cwd: config.cwd,
            env: { ...process.env, ...config.env },
            shell: config.shell !== false,
            stdio: ['pipe', 'pipe', 'pipe'],
        });
        const handle = {
            id: processId,
            pid: childProcess.pid || 0,
            config,
            status: types_1.ProcessStatus.STARTING,
            outputStream: childProcess.stdout,
            errorStream: childProcess.stderr,
        };
        // 监听进程事件
        this.setupProcessListeners(handle, childProcess);
        // 设置启动超时
        setTimeout(() => {
            if (handle.status === types_1.ProcessStatus.STARTING) {
                handle.status = types_1.ProcessStatus.RUNNING;
                this.logger.info(`进程启动完成: ${config.name}`);
            }
        }, 2000);
        return handle;
    }
    /**
     * 设置进程监听器
     */
    setupProcessListeners(handle, childProcess) {
        // 监听标准输出
        childProcess.stdout?.on('data', (data) => {
            this.eventBus.emitEvent(types_1.CoreEvents.PROCESS_OUTPUT, {
                processId: handle.id,
                data: data.toString(),
                type: 'stdout',
            });
        });
        // 监听错误输出
        childProcess.stderr?.on('data', (data) => {
            this.eventBus.emitEvent(types_1.CoreEvents.PROCESS_OUTPUT, {
                processId: handle.id,
                data: data.toString(),
                type: 'stderr',
            });
        });
        // 监听进程退出
        childProcess.on('exit', (exitCode, signal) => {
            const wasRunning = handle.status === types_1.ProcessStatus.RUNNING;
            handle.status = types_1.ProcessStatus.STOPPED;
            this.logger.info(`进程退出: ${handle.config.name} (code: ${exitCode}, signal: ${signal})`);
            if (wasRunning && exitCode !== 0) {
                // 非正常退出，可能是崩溃
                handle.status = types_1.ProcessStatus.CRASHED;
                this.eventBus.emitEvent(types_1.CoreEvents.PROCESS_CRASHED, {
                    processId: handle.id,
                    exitCode,
                    signal,
                });
            }
            else {
                this.eventBus.emitEvent(types_1.CoreEvents.PROCESS_STOPPED, {
                    processId: handle.id,
                    exitCode,
                    signal,
                });
            }
            // 从活动进程列表中移除
            this.processes.delete(handle.id);
        });
        // 监听进程错误
        childProcess.on('error', (error) => {
            this.logger.error(`进程错误: ${handle.config.name}`, error);
            handle.status = types_1.ProcessStatus.CRASHED;
            this.eventBus.emitEvent(types_1.CoreEvents.PROCESS_ERROR, {
                processId: handle.id,
                error: error.message,
            });
        });
    }
    /**
     * 停止进程
     */
    async stop(handle) {
        this.logger.info(`停止进程: ${handle.config.name}`);
        if (handle.status === types_1.ProcessStatus.STOPPED || handle.status === types_1.ProcessStatus.CRASHED) {
            this.logger.warn(`进程已停止: ${handle.config.name}`);
            return;
        }
        try {
            handle.status = types_1.ProcessStatus.STOPPING;
            // 尝试优雅关闭
            process.kill(handle.pid, 'SIGTERM');
            // 等待一段时间后强制关闭
            setTimeout(() => {
                if (this.processes.has(handle.id)) {
                    this.logger.warn(`强制关闭进程: ${handle.config.name}`);
                    process.kill(handle.pid, 'SIGKILL');
                }
            }, 5000);
        }
        catch (error) {
            this.logger.error(`停止进程失败: ${handle.config.name}`, error);
            throw error;
        }
    }
    /**
     * 停止所有进程
     */
    async stopAll() {
        this.logger.info('停止所有进程');
        const stopPromises = Array.from(this.processes.values())
            .filter(p => p.status === types_1.ProcessStatus.RUNNING || p.status === types_1.ProcessStatus.STARTING)
            .map(p => this.stop(p));
        await Promise.allSettled(stopPromises);
    }
    /**
     * 获取运行中的进程
     */
    getRunningProcesses() {
        return Array.from(this.processes.values())
            .filter(p => p.status === types_1.ProcessStatus.RUNNING || p.status === types_1.ProcessStatus.STARTING);
    }
    /**
     * 根据ID获取进程
     */
    getProcess(processId) {
        return this.processes.get(processId);
    }
    /**
     * 获取系统Shell
     */
    getShell() {
        return process.platform === 'win32' ? 'powershell.exe' : '/bin/bash';
    }
    /**
     * 获取Shell参数
     */
    getShellArgs(command, args) {
        if (process.platform === 'win32') {
            const fullCommand = args ? `${command} ${args.join(' ')}` : command;
            return ['-Command', fullCommand];
        }
        else {
            return ['-c', args ? `${command} ${args.join(' ')}` : command];
        }
    }
};
exports.ProcessLauncher = ProcessLauncher;
exports.ProcessLauncher = ProcessLauncher = __decorate([
    (0, inversify_1.injectable)(),
    __param(0, (0, inversify_1.inject)(types_1.TYPES.Logger)),
    __param(1, (0, inversify_1.inject)(types_1.TYPES.EventBus)),
    __metadata("design:paramtypes", [Object, event_bus_1.EventBus])
], ProcessLauncher);
