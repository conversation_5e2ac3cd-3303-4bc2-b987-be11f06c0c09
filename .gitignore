# 编译产物目录
dist/

# 源码目录中的编译文件
src/**/*.js
src/**/*.js.map

# 根目录和脚本目录中的编译文件
*.js
*.js.map
scripts/**/*.js
scripts/**/*.js.map

# 临时状态和日志文件
test-state.json
test.log
*.log

# Node.js 依赖
node_modules/

# 包管理器锁文件
package-lock.json
yarn.lock
pnpm-lock.yaml

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE 和编辑器文件
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 临时文件
*.tmp
*.temp
*.bak
*.backup

# 测试覆盖率报告
coverage/
*.lcov

# 构建工具缓存
.cache/
.parcel-cache/

# Electron 相关
out/
build/
release/

# 临时 HTML 测试文件
*-ui.html
*-ui-*.html
test-*.html
working-*.html
