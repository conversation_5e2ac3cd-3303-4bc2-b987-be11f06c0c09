/**
 * 工作流管理功能测试脚本
 * 测试工作流组管理、服务编排和批量操作功能
 */

import 'reflect-metadata';
import { Container } from 'inversify';
import { EventBus } from '../src/main/core/event-bus';
import { SimpleLogger } from '../src/main/services/logger-simple.service';
import { SimpleStateRepository } from '../src/main/services/state-simple.service';
import { WorkflowService } from '../src/main/services/workflow.service';
import { ServiceOrchestrator } from '../src/main/services/service-orchestrator.service';
import { HealthCheckService } from '../src/main/services/health-check.service';
import { BatchOperationsService } from '../src/main/services/batch-operations.service';
import { GitAdapter } from '../src/main/plugins/git/git-adapter';
import { ProcessLauncher } from '../src/main/services/process-launcher.service';
import { PortMonitor } from '../src/main/services/port-monitor.service';
import { ProjectDiscoveryService } from '../src/main/services/project-discovery.service';
import { TYPES, CoreEvents, IWorkflowGroup, IServiceConfig } from '../src/shared/types';

async function testWorkflowManagement() {
  console.log('🔄 开始测试工作流管理功能...\n');

  try {
    // 创建依赖注入容器
    const container = new Container();

    // 注册所有服务
    container.bind(TYPES.EventBus).to(EventBus).inSingletonScope();
    container.bind(TYPES.Logger).to(SimpleLogger).inSingletonScope();
    container.bind(TYPES.StateRepository).to(SimpleStateRepository).inSingletonScope();
    container.bind(TYPES.VersionControlAdapter).to(GitAdapter).inSingletonScope();
    container.bind(TYPES.ProjectDiscoveryService).to(ProjectDiscoveryService).inSingletonScope();
    container.bind(TYPES.ProcessLauncher).to(ProcessLauncher).inSingletonScope();
    container.bind(TYPES.PortMonitor).to(PortMonitor).inSingletonScope();
    container.bind(TYPES.WorkflowService).to(WorkflowService).inSingletonScope();
    container.bind(TYPES.ServiceOrchestrator).to(ServiceOrchestrator).inSingletonScope();
    container.bind(TYPES.HealthCheckService).to(HealthCheckService).inSingletonScope();
    container.bind(TYPES.BatchOperationsService).to(BatchOperationsService).inSingletonScope();

    console.log('✅ 依赖注入容器创建成功');

    // 获取服务实例
    const eventBus = container.get<EventBus>(TYPES.EventBus);
    const workflowService = container.get<WorkflowService>(TYPES.WorkflowService);
    const orchestrator = container.get<ServiceOrchestrator>(TYPES.ServiceOrchestrator);
    const healthCheck = container.get<HealthCheckService>(TYPES.HealthCheckService);
    const batchOps = container.get<BatchOperationsService>(TYPES.BatchOperationsService);

    // 监听事件
    let eventCount = 0;
    eventBus.on(CoreEvents.CONFIG_CHANGED, () => eventCount++);
    eventBus.on(CoreEvents.PROCESS_STARTED, () => eventCount++);
    eventBus.on(CoreEvents.PROCESS_STOPPED, () => eventCount++);

    console.log('✅ 事件监听器设置完成');

    // 测试1: 创建工作流组
    console.log('\n🧪 测试1: 创建工作流组...');

    const testServices: Omit<IServiceConfig, 'id'>[] = [
      {
        name: 'Frontend Dev Server',
        projectId: '.',
        command: 'echo "Starting frontend server"',
        port: 3000,
        autoRestart: true,
        restartDelay: 5000,
        orderIndex: 0,
        source: 'command',
      },
      {
        name: 'Backend API',
        projectId: '.',
        command: 'echo "Starting backend API"',
        port: 3001,
        autoRestart: true,
        restartDelay: 3000,
        dependsOn: [],
        orderIndex: 1,
        source: 'command',
      },
    ];

    const workflowGroup = await workflowService.createWorkflowGroup({
      name: '开发环境',
      description: '完整的开发环境工作流',
      services: testServices as IServiceConfig[],
    });

    console.log(`✅ 工作流组创建成功: ${workflowGroup.name} (${workflowGroup.services.length} 个服务)`);

    // 测试2: 验证工作流组
    console.log('\n🧪 测试2: 验证工作流组...');
    const validationErrors = await workflowService.validateWorkflowGroup(workflowGroup);

    if (validationErrors.length === 0) {
      console.log('✅ 工作流组验证通过');
    } else {
      console.log('⚠️ 工作流组验证发现问题:', validationErrors);
    }

    // 测试3: 启动工作流组
    console.log('\n🧪 测试3: 启动工作流组...');
    try {
      const execution = await orchestrator.startWorkflowGroup(workflowGroup);
      console.log(`✅ 工作流启动成功: ${execution.id}`);
      console.log(`状态: ${execution.status}, 服务数: ${execution.services.length}`);

      // 等待一段时间
      await new Promise(resolve => setTimeout(resolve, 3000));

      // 检查执行状态
      const currentExecution = orchestrator.getWorkflowExecution(execution.id);
      if (currentExecution) {
        console.log(`当前状态: ${currentExecution.status}`);
        currentExecution.services.forEach(s => {
          console.log(`  - ${s.serviceName}: ${s.status}`);
        });
      }

      // 停止工作流
      console.log('\n🧪 测试4: 停止工作流组...');
      await orchestrator.stopWorkflowGroup(execution.id);
      console.log('✅ 工作流停止成功');

    } catch (error) {
      console.log('⚠️ 工作流启动测试跳过（需要实际的项目环境）');
    }

    // 测试5: 健康检查服务
    console.log('\n🧪 测试5: 健康检查服务...');

    const testService: IServiceConfig = {
      id: 'test-service',
      name: 'Test Service',
      projectId: '.',
      command: 'echo "test"',
      port: 8080,
      healthCheckUrl: 'http://localhost:8080/health',
      autoRestart: true,
      orderIndex: 0,
      source: 'command',
    };

    const healthResult = await healthCheck.checkServiceHealth(testService);
    console.log(`健康检查结果: ${healthResult.healthy ? '健康' : '不健康'}`);
    console.log(`响应时间: ${healthResult.responseTime}ms`);

    if (!healthResult.healthy && healthResult.error) {
      console.log(`错误信息: ${healthResult.error}`);
    }

    console.log('✅ 健康检查服务测试完成');

    // 测试6: 批量操作服务
    console.log('\n🧪 测试6: 批量操作服务...');

    // 模拟项目ID列表
    const projectIds = ['project1', 'project2'];

    try {
      const batchResult = await batchOps.batchGitStatus(projectIds);
      console.log(`批量Git状态检查完成:`);
      console.log(`- 总数: ${batchResult.totalCount}`);
      console.log(`- 成功: ${batchResult.successCount}`);
      console.log(`- 失败: ${batchResult.failureCount}`);
      console.log(`- 耗时: ${batchResult.duration}ms`);
    } catch (error) {
      console.log('⚠️ 批量操作测试跳过（需要实际的项目）');
    }

    // 测试7: 工作流组管理
    console.log('\n🧪 测试7: 工作流组管理...');

    // 获取所有工作流组
    const allGroups = await workflowService.getWorkflowGroups();
    console.log(`当前工作流组数量: ${allGroups.length}`);

    // 更新工作流组
    await workflowService.updateWorkflowGroup(workflowGroup.id, {
      description: '更新后的描述',
    });
    console.log('✅ 工作流组更新成功');

    // 导出工作流组
    const exportData = await workflowService.exportWorkflowGroup(workflowGroup.id);
    console.log('✅ 工作流组导出成功');

    // 删除工作流组
    await workflowService.deleteWorkflowGroup(workflowGroup.id);
    console.log('✅ 工作流组删除成功');

    // 测试8: 获取活动执行
    console.log('\n🧪 测试8: 获取活动执行...');
    const activeExecutions = orchestrator.getActiveExecutions();
    console.log(`当前活动执行数量: ${activeExecutions.length}`);

    // 停止所有执行
    await orchestrator.stopAllExecutions();
    console.log('✅ 所有执行已停止');

    // 停止所有健康监控
    await healthCheck.stopAllMonitoring();
    console.log('✅ 所有健康监控已停止');

    // 统计信息
    console.log('\n📊 测试统计:');
    console.log(`- 触发事件数量: ${eventCount}`);
    console.log(`- 工作流组操作: 创建、更新、导出、删除`);
    console.log(`- 服务编排: 启动、停止、状态检查`);
    console.log(`- 健康检查: 服务健康状态检测`);
    console.log(`- 批量操作: Git状态批量检查`);

    console.log('\n🎉 工作流管理功能测试完成！');

  } catch (error) {
    console.error('\n❌ 测试失败:', error);
    process.exit(1);
  }
}

// 运行测试
testWorkflowManagement().catch(console.error);
