"use strict";
/**
 * 端口监控功能测试脚本
 * 测试端口状态检测和监控功能
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
require("reflect-metadata");
const inversify_1 = require("inversify");
const event_bus_1 = require("../src/main/core/event-bus");
const logger_simple_service_1 = require("../src/main/services/logger-simple.service");
const port_monitor_service_1 = require("../src/main/services/port-monitor.service");
const types_1 = require("../src/shared/types");
const net = __importStar(require("net"));
async function testPortMonitoring() {
    console.log('🔌 开始测试端口监控功能...\n');
    try {
        // 创建依赖注入容器
        const container = new inversify_1.Container();
        container.bind(types_1.TYPES.EventBus).to(event_bus_1.EventBus).inSingletonScope();
        container.bind(types_1.TYPES.Logger).to(logger_simple_service_1.SimpleLogger).inSingletonScope();
        container.bind(types_1.TYPES.PortMonitor).to(port_monitor_service_1.PortMonitor).inSingletonScope();
        const eventBus = container.get(types_1.TYPES.EventBus);
        const portMonitor = container.get(types_1.TYPES.PortMonitor);
        console.log('✅ 依赖注入容器创建成功');
        // 监听端口事件
        let portEvents = [];
        eventBus.on(types_1.CoreEvents.PORT_AVAILABLE, (event) => {
            portEvents.push(`${event.payload.port}:AVAILABLE`);
            console.log(`✅ 端口可用: ${event.payload.port}`);
        });
        eventBus.on(types_1.CoreEvents.PORT_UNAVAILABLE, (event) => {
            portEvents.push(`${event.payload.port}:UNAVAILABLE`);
            console.log(`❌ 端口不可用: ${event.payload.port}`);
        });
        console.log('✅ 事件监听器设置完成');
        // 测试1: 检查常见端口
        console.log('\n🧪 测试1: 检查常见端口状态...');
        const commonPorts = [80, 443, 3000, 5173, 8080];
        for (const port of commonPorts) {
            const available = await portMonitor.checkPort(port);
            console.log(`端口 ${port}: ${available ? '可用' : '被占用'}`);
        }
        // 测试2: 批量检查端口
        console.log('\n🧪 测试2: 批量检查端口...');
        const testPorts = [3001, 3002, 3003, 3004, 3005];
        const results = await portMonitor.checkMultiplePorts(testPorts);
        console.log('批量检查结果:');
        results.forEach((available, port) => {
            console.log(`- 端口 ${port}: ${available ? '可用' : '被占用'}`);
        });
        // 测试3: 创建测试服务器并监控
        console.log('\n🧪 测试3: 创建测试服务器并监控...');
        const testPort = 3333;
        // 先检查端口是否可用
        const initiallyAvailable = await portMonitor.checkPort(testPort);
        console.log(`测试端口 ${testPort} 初始状态: ${initiallyAvailable ? '可用' : '被占用'}`);
        // 开始监控端口
        console.log(`开始监控端口 ${testPort}...`);
        const watcher = portMonitor.watch(testPort, (available) => {
            console.log(`🔍 端口 ${testPort} 状态变化: ${available ? '可用' : '被占用'}`);
        });
        // 等待一下让监控开始
        await new Promise(resolve => setTimeout(resolve, 1000));
        // 创建测试服务器
        console.log(`在端口 ${testPort} 上启动测试服务器...`);
        const server = net.createServer();
        await new Promise((resolve, reject) => {
            server.listen(testPort, () => {
                console.log(`✅ 测试服务器已启动在端口 ${testPort}`);
                resolve();
            });
            server.on('error', (error) => {
                console.log(`❌ 服务器启动失败: ${error.message}`);
                reject(error);
            });
        });
        // 等待监控检测到变化
        await new Promise(resolve => setTimeout(resolve, 3000));
        // 停止服务器
        console.log('停止测试服务器...');
        await new Promise((resolve) => {
            server.close(() => {
                console.log('✅ 测试服务器已停止');
                resolve();
            });
        });
        // 等待监控检测到变化
        await new Promise(resolve => setTimeout(resolve, 3000));
        // 停止监控
        console.log('停止端口监控...');
        watcher.stop();
        // 测试4: 获取监控的端口列表
        console.log('\n🧪 测试4: 获取监控状态...');
        const watchedPorts = portMonitor.getWatchedPorts();
        console.log(`当前监控的端口: ${watchedPorts.join(', ')}`);
        // 停止所有监控
        console.log('停止所有端口监控...');
        portMonitor.stopAll();
        // 统计信息
        console.log('\n📊 测试统计:');
        console.log(`- 触发的端口事件: ${portEvents.join(', ')}`);
        console.log(`- 事件总数: ${portEvents.length}`);
        console.log(`- 检查的端口数量: ${commonPorts.length + testPorts.length + 1}`);
        console.log('\n🎉 端口监控功能测试完成！');
    }
    catch (error) {
        console.error('\n❌ 测试失败:', error);
        process.exit(1);
    }
}
// 运行测试
testPortMonitoring().catch(console.error);
