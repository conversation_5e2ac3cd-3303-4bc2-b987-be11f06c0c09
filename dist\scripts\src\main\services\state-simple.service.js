"use strict";
/**
 * 简化版状态存储服务实现
 * 不依赖 electron-store，用于测试
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SimpleStateRepository = void 0;
const inversify_1 = require("inversify");
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
let SimpleStateRepository = class SimpleStateRepository {
    constructor() {
        this.data = {};
        this.filePath = path.join(process.cwd(), 'test-state.json');
        this.loadFromFile();
    }
    async get(key) {
        return this.data[key];
    }
    async set(key, value) {
        this.data[key] = value;
        await this.saveToFile();
    }
    async delete(key) {
        delete this.data[key];
        await this.saveToFile();
    }
    async getAll() {
        return { ...this.data };
    }
    async has(key) {
        return key in this.data;
    }
    async clear() {
        this.data = {};
        await this.saveToFile();
    }
    loadFromFile() {
        try {
            if (fs.existsSync(this.filePath)) {
                const content = fs.readFileSync(this.filePath, 'utf-8');
                this.data = JSON.parse(content);
            }
        }
        catch (error) {
            console.warn('Failed to load state from file:', error);
            this.data = {};
        }
    }
    async saveToFile() {
        try {
            const content = JSON.stringify(this.data, null, 2);
            fs.writeFileSync(this.filePath, content, 'utf-8');
        }
        catch (error) {
            console.error('Failed to save state to file:', error);
        }
    }
};
exports.SimpleStateRepository = SimpleStateRepository;
exports.SimpleStateRepository = SimpleStateRepository = __decorate([
    (0, inversify_1.injectable)(),
    __metadata("design:paramtypes", [])
], SimpleStateRepository);
