/**
 * 项目清理后的功能验证测试
 * 验证清理后的项目结构和功能是否正常工作
 */

import 'reflect-metadata';
import { Container } from 'inversify';
import { EventBus } from '../../src/main/core/event-bus';
import { SimpleLogger } from '../../src/main/services/logger-simple.service';
import { SimpleStateRepository } from '../../src/main/services/state-simple.service';
import { ProjectDiscoveryService } from '../../src/main/services/project-discovery.service';
import { WorkflowService } from '../../src/main/services/workflow.service';
import { HealthCheckService } from '../../src/main/services/health-check.service';
import { TYPES, IServiceConfig, IWorkflowGroup } from '../../src/shared/types';

describe('项目清理后功能验证', () => {
  let container: Container;
  let eventBus: EventBus;
  let logger: SimpleLogger;
  let stateRepository: SimpleStateRepository;
  let projectDiscovery: ProjectDiscoveryService;
  let workflowService: WorkflowService;
  let healthCheck: HealthCheckService;

  beforeEach(() => {
    // 初始化依赖注入容器
    container = new Container();
    
    // 创建服务实例
    eventBus = new EventBus();
    logger = new SimpleLogger();
    stateRepository = new SimpleStateRepository();
    projectDiscovery = new ProjectDiscoveryService(eventBus, logger, stateRepository);
    workflowService = new WorkflowService(eventBus, logger, stateRepository);
    healthCheck = new HealthCheckService(eventBus, logger);

    // 绑定到容器
    container.bind(TYPES.EventBus).toConstantValue(eventBus);
    container.bind(TYPES.Logger).toConstantValue(logger);
    container.bind(TYPES.StateRepository).toConstantValue(stateRepository);
    container.bind(TYPES.ProjectDiscoveryService).toConstantValue(projectDiscovery);
    container.bind(TYPES.WorkflowService).toConstantValue(workflowService);
    container.bind(TYPES.HealthCheckService).toConstantValue(healthCheck);
  });

  afterEach(() => {
    container.unbindAll();
  });

  describe('核心服务初始化', () => {
    test('所有核心服务应该正确初始化', () => {
      expect(eventBus).toBeDefined();
      expect(logger).toBeDefined();
      expect(stateRepository).toBeDefined();
      expect(projectDiscovery).toBeDefined();
      expect(workflowService).toBeDefined();
      expect(healthCheck).toBeDefined();
    });

    test('依赖注入容器应该正确绑定服务', () => {
      expect(container.get(TYPES.EventBus)).toBe(eventBus);
      expect(container.get(TYPES.Logger)).toBe(logger);
      expect(container.get(TYPES.StateRepository)).toBe(stateRepository);
      expect(container.get(TYPES.ProjectDiscoveryService)).toBe(projectDiscovery);
      expect(container.get(TYPES.WorkflowService)).toBe(workflowService);
      expect(container.get(TYPES.HealthCheckService)).toBe(healthCheck);
    });
  });

  describe('项目发现功能', () => {
    test('应该能够扫描项目目录', async () => {
      const projects = await projectDiscovery.scanDirectory(process.cwd());
      expect(Array.isArray(projects)).toBe(true);
    });

    test('应该能够获取项目列表', () => {
      const projects = projectDiscovery.getProjects();
      expect(Array.isArray(projects)).toBe(true);
    });
  });

  describe('工作流管理功能', () => {
    test('应该能够创建工作流组', async () => {
      const testServices: Omit<IServiceConfig, 'id'>[] = [
        {
          name: 'Test Service',
          projectId: 'test-project',
          command: 'echo "test"',
          port: 3000,
          orderIndex: 0,
          source: 'command',
        },
      ];

      const workflowGroup = await workflowService.createWorkflowGroup({
        name: '测试工作流',
        description: '用于测试的工作流组',
        services: testServices,
      });

      expect(workflowGroup).toBeDefined();
      expect(workflowGroup.id).toBeDefined();
      expect(workflowGroup.name).toBe('测试工作流');
      expect(workflowGroup.services).toHaveLength(1);
      expect(workflowGroup.services[0].name).toBe('Test Service');
    });

    test('应该能够获取工作流组列表', async () => {
      const groups = await workflowService.getWorkflowGroups();
      expect(Array.isArray(groups)).toBe(true);
    });

    test('应该能够向工作流组添加服务', async () => {
      // 先创建一个工作流组
      const workflowGroup = await workflowService.createWorkflowGroup({
        name: '测试工作流',
        description: '用于测试的工作流组',
        services: [],
      });

      // 添加服务
      const newService: Omit<IServiceConfig, 'id'> = {
        name: 'New Test Service',
        projectId: 'test-project',
        command: 'echo "new test"',
        port: 3001,
        orderIndex: 0,
        source: 'command',
      };

      await workflowService.addServiceToGroup(workflowGroup.id, newService);

      // 验证服务已添加
      const updatedGroup = await workflowService.getWorkflowGroup(workflowGroup.id);
      expect(updatedGroup).toBeDefined();
      expect(updatedGroup!.services).toHaveLength(1);
      expect(updatedGroup!.services[0].name).toBe('New Test Service');
    });
  });

  describe('健康检查功能', () => {
    test('应该能够检查服务健康状态', async () => {
      const testService: IServiceConfig = {
        id: 'test-service',
        name: 'Test Service',
        projectId: 'test-project',
        command: 'echo "test"',
        port: 8080,
        healthCheckUrl: 'http://localhost:8080/health',
        orderIndex: 0,
        source: 'command',
      };

      const healthResult = await healthCheck.checkServiceHealth(testService);
      
      expect(healthResult).toBeDefined();
      expect(typeof healthResult.healthy).toBe('boolean');
      expect(typeof healthResult.responseTime).toBe('number');
      expect(healthResult.timestamp).toBeInstanceOf(Date);
    });
  });

  describe('事件系统', () => {
    test('应该能够发布和订阅事件', (done) => {
      const testEvent = 'test-event';
      const testData = { message: 'test message' };

      eventBus.subscribe(testEvent, (data) => {
        expect(data).toEqual(testData);
        done();
      });

      eventBus.emitEvent(testEvent, testData);
    });
  });

  describe('状态持久化', () => {
    test('应该能够保存和获取状态', async () => {
      const testKey = 'test-key';
      const testValue = { data: 'test data' };

      await stateRepository.setState(testKey, testValue);
      const retrievedValue = await stateRepository.getState(testKey);

      expect(retrievedValue).toEqual(testValue);
    });

    test('应该能够删除状态', async () => {
      const testKey = 'test-key-to-delete';
      const testValue = { data: 'test data' };

      await stateRepository.setState(testKey, testValue);
      await stateRepository.deleteState(testKey);
      const retrievedValue = await stateRepository.getState(testKey);

      expect(retrievedValue).toBeNull();
    });
  });

  describe('类型定义验证', () => {
    test('IServiceConfig 应该包含所有必需字段', () => {
      const serviceConfig: IServiceConfig = {
        id: 'test-id',
        projectId: 'test-project',
        name: 'Test Service',
        command: 'echo "test"',
        orderIndex: 0,
        source: 'command',
      };

      expect(serviceConfig.id).toBeDefined();
      expect(serviceConfig.projectId).toBeDefined();
      expect(serviceConfig.name).toBeDefined();
      expect(serviceConfig.command).toBeDefined();
      expect(serviceConfig.orderIndex).toBeDefined();
      expect(serviceConfig.source).toBeDefined();
    });

    test('IWorkflowGroup 应该包含所有必需字段', () => {
      const workflowGroup: IWorkflowGroup = {
        id: 'test-id',
        name: 'Test Group',
        services: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      expect(workflowGroup.id).toBeDefined();
      expect(workflowGroup.name).toBeDefined();
      expect(workflowGroup.services).toBeDefined();
      expect(workflowGroup.createdAt).toBeDefined();
      expect(workflowGroup.updatedAt).toBeDefined();
    });
  });
});
