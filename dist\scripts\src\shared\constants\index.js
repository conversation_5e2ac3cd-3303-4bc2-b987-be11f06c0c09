"use strict";
/**
 * 共享常量定义
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.IPC_CHANNELS = exports.LOG_LEVELS = exports.THEMES = exports.FILE_EXTENSIONS = exports.COMMON_COMMANDS = exports.GIT_PATTERNS = exports.PROJECT_PATTERNS = exports.DEFAULT_PORTS = exports.APP_CONFIG = void 0;
exports.APP_CONFIG = {
    NAME: 'DevWorkbench',
    VERSION: '0.1.0',
    DESCRIPTION: '个人本地开发工作台',
};
exports.DEFAULT_PORTS = {
    VITE_DEV: 5173,
    REACT_DEV: 3000,
    NEXT_DEV: 3000,
    EXPRESS: 3001,
    NEST: 3000,
    SPRING_BOOT: 8080,
    DJANGO: 8000,
    FLASK: 5000,
};
exports.PROJECT_PATTERNS = {
    PACKAGE_JSON: 'package.json',
    REQUIREMENTS_TXT: 'requirements.txt',
    POM_XML: 'pom.xml',
    BUILD_GRADLE: 'build.gradle',
    CARGO_TOML: 'Cargo.toml',
    GO_MOD: 'go.mod',
    CSPROJ: '*.csproj',
    SLN: '*.sln',
};
exports.GIT_PATTERNS = {
    GIT_DIR: '.git',
    GITIGNORE: '.gitignore',
    GITMODULES: '.gitmodules',
};
exports.COMMON_COMMANDS = {
    NODE: {
        INSTALL: 'npm install',
        START: 'npm start',
        DEV: 'npm run dev',
        BUILD: 'npm run build',
        TEST: 'npm test',
    },
    PYTHON: {
        INSTALL: 'pip install -r requirements.txt',
        START: 'python main.py',
        DEV: 'python manage.py runserver',
        TEST: 'python -m pytest',
    },
    JAVA: {
        INSTALL: 'mvn install',
        START: 'mvn spring-boot:run',
        BUILD: 'mvn clean package',
        TEST: 'mvn test',
    },
};
exports.FILE_EXTENSIONS = {
    TYPESCRIPT: ['.ts', '.tsx'],
    JAVASCRIPT: ['.js', '.jsx'],
    PYTHON: ['.py'],
    JAVA: ['.java'],
    CSHARP: ['.cs'],
    GO: ['.go'],
    RUST: ['.rs'],
};
exports.THEMES = {
    LIGHT: 'light',
    DARK: 'dark',
    AUTO: 'auto',
};
exports.LOG_LEVELS = {
    DEBUG: 'debug',
    INFO: 'info',
    WARN: 'warn',
    ERROR: 'error',
};
exports.IPC_CHANNELS = {
    // 项目管理
    PROJECT_SCAN: 'project:scan',
    PROJECT_GET_ALL: 'project:get-all',
    PROJECT_REFRESH: 'project:refresh',
    PROJECT_OPEN_IN_VSCODE: 'project:open-in-vscode',
    PROJECT_OPEN_IN_TERMINAL: 'project:open-in-terminal',
    PROJECT_OPEN_IN_EXPLORER: 'project:open-in-explorer',
    // Git 操作
    GIT_STATUS: 'git:status',
    GIT_PULL: 'git:pull',
    GIT_BRANCH: 'git:branch',
    // 进程管理
    PROCESS_START: 'process:start',
    PROCESS_STOP: 'process:stop',
    PROCESS_STOP_ALL: 'process:stop-all',
    PROCESS_GET_RUNNING: 'process:get-running',
    // 端口监控
    PORT_CHECK: 'port:check',
    PORT_WATCH: 'port:watch',
    // 配置管理
    CONFIG_GET: 'config:get',
    CONFIG_SET: 'config:set',
    // 事件
    EVENT_EMIT: 'event:emit',
    EVENT_SUBSCRIBE: 'event:subscribe',
};
