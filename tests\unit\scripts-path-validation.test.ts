/**
 * 脚本路径修复验证测试
 * 验证移动到 scripts 目录后的脚本文件路径引用是否正确
 */

import * as fs from 'fs';
import * as path from 'path';

describe('脚本路径修复验证', () => {
  const scriptsDir = path.join(process.cwd(), 'scripts');
  
  beforeAll(() => {
    // 确保 scripts 目录存在
    expect(fs.existsSync(scriptsDir)).toBe(true);
  });

  describe('scripts 目录结构', () => {
    test('scripts 目录应该存在', () => {
      expect(fs.existsSync(scriptsDir)).toBe(true);
      expect(fs.statSync(scriptsDir).isDirectory()).toBe(true);
    });

    test('应该包含所有预期的脚本文件', () => {
      const expectedFiles = [
        'cli.ts',
        'demo.ts',
        'integration-test.ts',
        'interactive.ts',
        'test-core.ts',
        'test-port-monitoring.ts',
        'test-process-management.ts',
        'test-project-discovery.ts',
        'test-web-enhanced.ts',
        'test-workflow-management.ts',
        'web-enhanced.ts',
        'web-server-enhanced.ts',
        'web-server.ts',
      ];

      const actualFiles = fs.readdirSync(scriptsDir).filter(file => file.endsWith('.ts'));
      
      expectedFiles.forEach(expectedFile => {
        expect(actualFiles).toContain(expectedFile);
      });
    });
  });

  describe('路径引用修复验证', () => {
    const scriptFiles = fs.readdirSync(scriptsDir).filter(file => file.endsWith('.ts'));

    scriptFiles.forEach(scriptFile => {
      test(`${scriptFile} 应该使用正确的相对路径引用`, () => {
        const filePath = path.join(scriptsDir, scriptFile);
        const content = fs.readFileSync(filePath, 'utf8');

        // 检查是否有错误的路径引用（应该已经被修复）
        expect(content).not.toMatch(/from '\.\/src\//);
        expect(content).not.toMatch(/import '\.\/src\//);

        // 检查是否有正确的路径引用
        if (content.includes("from '../src/")) {
          expect(content).toMatch(/from '\.\.\/src\//);
        }
        if (content.includes("import '../src/")) {
          expect(content).toMatch(/import '\.\.\/src\//);
        }
      });
    });

    test('所有脚本文件都应该能够找到引用的模块', () => {
      const scriptFiles = fs.readdirSync(scriptsDir).filter(file => file.endsWith('.ts'));
      
      scriptFiles.forEach(scriptFile => {
        const filePath = path.join(scriptsDir, scriptFile);
        const content = fs.readFileSync(filePath, 'utf8');
        
        // 提取所有相对路径导入
        const importMatches = content.match(/from '\.\.\/src\/[^']+'/g) || [];
        const requireMatches = content.match(/import '\.\.\/src\/[^']+'/g) || [];
        
        [...importMatches, ...requireMatches].forEach(importStatement => {
          const relativePath = importStatement.match(/'([^']+)'/)?.[1];
          if (relativePath) {
            const absolutePath = path.resolve(scriptsDir, relativePath);
            const tsPath = absolutePath + '.ts';
            const indexPath = path.join(absolutePath, 'index.ts');
            
            // 检查文件是否存在（.ts 文件或 index.ts）
            const exists = fs.existsSync(tsPath) || fs.existsSync(indexPath);
            expect(exists).toBe(true);
          }
        });
      });
    });
  });

  describe('根目录清理验证', () => {
    test('根目录不应该包含编译后的 .js 文件', () => {
      const rootFiles = fs.readdirSync(process.cwd());
      const jsFiles = rootFiles.filter(file => 
        file.endsWith('.js') && 
        !file.includes('jest.config') && 
        !file.includes('vite.config')
      );
      
      expect(jsFiles).toHaveLength(0);
    });

    test('根目录不应该包含临时 HTML 文件', () => {
      const rootFiles = fs.readdirSync(process.cwd());
      const htmlFiles = rootFiles.filter(file => 
        file.endsWith('.html') && 
        (file.includes('test-') || file.includes('enhanced-') || file.includes('working-'))
      );
      
      expect(htmlFiles).toHaveLength(0);
    });

    test('根目录不应该包含临时配置文件', () => {
      const rootFiles = fs.readdirSync(process.cwd());
      
      expect(rootFiles).not.toContain('package-simple.json');
      expect(rootFiles).not.toContain('test-state.json');
    });
  });

  describe('源码目录清理验证', () => {
    test('src 目录不应该包含编译后的 .js 文件', () => {
      const srcDir = path.join(process.cwd(), 'src');
      
      function checkDirectory(dir: string) {
        const files = fs.readdirSync(dir);
        
        files.forEach(file => {
          const filePath = path.join(dir, file);
          const stat = fs.statSync(filePath);
          
          if (stat.isDirectory()) {
            checkDirectory(filePath);
          } else if (file.endsWith('.js')) {
            // src 目录中不应该有 .js 文件
            fail(`发现编译产物文件: ${filePath}`);
          }
        });
      }
      
      if (fs.existsSync(srcDir)) {
        checkDirectory(srcDir);
      }
    });
  });

  describe('.gitignore 文件验证', () => {
    test('.gitignore 文件应该存在', () => {
      const gitignorePath = path.join(process.cwd(), '.gitignore');
      expect(fs.existsSync(gitignorePath)).toBe(true);
    });

    test('.gitignore 应该包含必要的忽略规则', () => {
      const gitignorePath = path.join(process.cwd(), '.gitignore');
      const content = fs.readFileSync(gitignorePath, 'utf8');
      
      // 检查关键的忽略规则
      expect(content).toMatch(/dist\//);
      expect(content).toMatch(/\*\.js/);
      expect(content).toMatch(/node_modules\//);
      expect(content).toMatch(/\*\.log/);
      expect(content).toMatch(/\.DS_Store/);
    });
  });

  describe('package.json 脚本路径验证', () => {
    test('package.json 中的脚本应该指向 scripts 目录', () => {
      const packageJsonPath = path.join(process.cwd(), 'package.json');
      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
      
      const scripts = packageJson.scripts || {};
      
      // 检查关键脚本是否指向正确的路径
      if (scripts.cli) {
        expect(scripts.cli).toMatch(/scripts\/cli\.ts/);
      }
      if (scripts.demo) {
        expect(scripts.demo).toMatch(/scripts\/demo\.ts/);
      }
      if (scripts.interactive) {
        expect(scripts.interactive).toMatch(/scripts\/interactive\.ts/);
      }
      if (scripts.web) {
        expect(scripts.web).toMatch(/scripts\/web-server\.ts/);
      }
      if (scripts['web-enhanced']) {
        expect(scripts['web-enhanced']).toMatch(/scripts\/web-enhanced\.ts/);
      }
    });

    test('package.json 脚本应该输出到 dist/scripts 目录', () => {
      const packageJsonPath = path.join(process.cwd(), 'package.json');
      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
      
      const scripts = packageJson.scripts || {};
      
      // 检查编译输出路径
      Object.values(scripts).forEach((script: any) => {
        if (typeof script === 'string' && script.includes('--outDir')) {
          expect(script).toMatch(/--outDir dist\/scripts/);
        }
      });
    });
  });
});
