<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DevWorkbench Enhanced - 项目管理工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }

        .card h3 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.3rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .card-icon {
            width: 24px;
            height: 24px;
            background: #667eea;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
        }

        .api-links {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .api-link {
            display: inline-block;
            padding: 12px 20px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            text-align: center;
            font-weight: 500;
        }

        .api-link:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-online {
            background: #4CAF50;
            box-shadow: 0 0 10px rgba(76, 175, 80, 0.5);
        }

        .status-offline {
            background: #f44336;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .feature-item {
            background: #f8f9ff;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }

        .feature-item h4 {
            color: #667eea;
            margin-bottom: 8px;
        }

        .feature-item p {
            color: #666;
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .footer {
            text-align: center;
            margin-top: 40px;
            color: white;
            opacity: 0.8;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error-message {
            background: #ffebee;
            color: #c62828;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #f44336;
            margin-top: 15px;
        }

        .success-message {
            background: #e8f5e8;
            color: #2e7d32;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #4caf50;
            margin-top: 15px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .dashboard {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 DevWorkbench Enhanced</h1>
            <p>现代化项目管理和开发工具集</p>
        </div>

        <div class="dashboard">
            <div class="card">
                <h3>
                    <span class="card-icon">🔗</span>
                    API 服务状态
                </h3>
                <p>
                    <span class="status-indicator status-online"></span>
                    服务运行中 - 端口 3000
                </p>
                <div class="api-links">
                    <a href="/api/docs" class="api-link" target="_blank">📚 API文档</a>
                    <a href="/api/projects" class="api-link" target="_blank">📁 项目列表</a>
                    <a href="/api/status" class="api-link" target="_blank">📊 系统状态</a>
                    <a href="/api/projects/n8n" class="api-link" target="_blank">🔄 n8n格式数据</a>
                </div>
            </div>

            <div class="card">
                <h3>
                    <span class="card-icon">⚡</span>
                    快速操作
                </h3>
                <div class="api-links">
                    <button class="api-link" onclick="scanProjects()">🔍 扫描项目</button>
                    <button class="api-link" onclick="refreshData()">🔄 刷新数据</button>
                    <button class="api-link" onclick="openTerminal()">💻 打开终端</button>
                    <button class="api-link" onclick="openVSCode()">📝 打开VS Code</button>
                </div>
                <div id="actionResult"></div>
            </div>

            <div class="card">
                <h3>
                    <span class="card-icon">📈</span>
                    系统信息
                </h3>
                <div id="systemInfo">
                    <div class="loading"></div>
                    <span>加载系统信息中...</span>
                </div>
            </div>
        </div>

        <div class="card">
            <h3>
                <span class="card-icon">✨</span>
                核心功能特性
            </h3>
            <div class="features-grid">
                <div class="feature-item">
                    <h4>🔍 智能项目发现</h4>
                    <p>自动扫描和识别各种类型的开发项目，支持 Git 仓库检测</p>
                </div>
                <div class="feature-item">
                    <h4>🚀 一键启动</h4>
                    <p>快速在 VS Code、终端或文件管理器中打开项目</p>
                </div>
                <div class="feature-item">
                    <h4>📊 实时状态</h4>
                    <p>监控项目状态、Git 分支信息和未提交更改</p>
                </div>
                <div class="feature-item">
                    <h4>🔄 工作流管理</h4>
                    <p>创建和管理复杂的开发工作流，支持服务编排</p>
                </div>
                <div class="feature-item">
                    <h4>🌐 RESTful API</h4>
                    <p>完整的 API 接口，支持第三方工具集成</p>
                </div>
                <div class="feature-item">
                    <h4>🎨 现代界面</h4>
                    <p>响应式设计，支持深色模式和自定义主题</p>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>© 2024 DevWorkbench Enhanced - 让开发更高效 🐱</p>
        </div>
    </div>

    <script>
        // 加载系统信息
        async function loadSystemInfo() {
            try {
                const response = await fetch('/api/status');
                const data = await response.json();
                
                const systemInfoDiv = document.getElementById('systemInfo');
                systemInfoDiv.innerHTML = `
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                        <div>
                            <strong>运行时间:</strong><br>
                            <span style="color: #667eea;">${data.uptime || '未知'}</span>
                        </div>
                        <div>
                            <strong>项目数量:</strong><br>
                            <span style="color: #667eea;">${data.projectCount || 0} 个</span>
                        </div>
                        <div>
                            <strong>内存使用:</strong><br>
                            <span style="color: #667eea;">${data.memoryUsage || '未知'}</span>
                        </div>
                        <div>
                            <strong>版本:</strong><br>
                            <span style="color: #667eea;">${data.version || '0.1.0'}</span>
                        </div>
                    </div>
                `;
            } catch (error) {
                document.getElementById('systemInfo').innerHTML = `
                    <div class="error-message">
                        无法加载系统信息: ${error.message}
                    </div>
                `;
            }
        }

        // 快速操作函数
        async function scanProjects() {
            showActionResult('正在扫描项目...', 'loading');
            try {
                const response = await fetch('/api/projects/scan', { method: 'POST' });
                const result = await response.json();
                showActionResult(`扫描完成！发现 ${result.count || 0} 个项目`, 'success');
            } catch (error) {
                showActionResult(`扫描失败: ${error.message}`, 'error');
            }
        }

        async function refreshData() {
            showActionResult('正在刷新数据...', 'loading');
            try {
                await loadSystemInfo();
                showActionResult('数据刷新成功！', 'success');
            } catch (error) {
                showActionResult(`刷新失败: ${error.message}`, 'error');
            }
        }

        function openTerminal() {
            showActionResult('正在打开终端...', 'loading');
            // 这里可以调用 Electron API 或发送请求到后端
            setTimeout(() => {
                showActionResult('终端打开请求已发送', 'success');
            }, 1000);
        }

        function openVSCode() {
            showActionResult('正在打开 VS Code...', 'loading');
            // 这里可以调用 Electron API 或发送请求到后端
            setTimeout(() => {
                showActionResult('VS Code 打开请求已发送', 'success');
            }, 1000);
        }

        function showActionResult(message, type) {
            const resultDiv = document.getElementById('actionResult');
            let className = '';
            let icon = '';
            
            switch(type) {
                case 'loading':
                    className = '';
                    icon = '<div class="loading" style="display: inline-block; margin-right: 10px;"></div>';
                    break;
                case 'success':
                    className = 'success-message';
                    icon = '✅ ';
                    break;
                case 'error':
                    className = 'error-message';
                    icon = '❌ ';
                    break;
            }
            
            resultDiv.innerHTML = `<div class="${className}">${icon}${message}</div>`;
            
            if (type !== 'loading') {
                setTimeout(() => {
                    resultDiv.innerHTML = '';
                }, 3000);
            }
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            loadSystemInfo();
        });
    </script>
</body>
</html>
